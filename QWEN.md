# Qwen Code Agent Guidelines for Makulu Combat Rotations

This document provides specific guidelines and rules for the Qwen Code Agent when working with the Makulu Combat Rotations framework for World of Warcraft.

## Core Principles

1. **Always Follow Existing Patterns**: The Makulu framework has established conventions. Follow the existing code structure, naming conventions, and organization patterns.
2. **Preserve Framework Integrity**: Never modify core framework files in `/Rotations/MakuluFramework/`. Only work within class-specific rotation files.
3. **Performance First**: Use caching appropriately and avoid expensive operations in hot paths.

## Agent Goals and Objectives

The primary goal of the Qwen Code Agent when working with Makulu Combat Rotations is to:

### Primary Objective
Convert raw SimulationCraft APL (Action Priority List) into fully functional, optimized Makulu Framework Rotation LUA files that:
- Maintain 100% functional parity with the original APL logic
- Follow all Makulu Framework conventions and best practices
- Are performance-optimized for real-time combat execution
- Include proper error handling and edge case management

### Conversion Process
1. **APL Analysis**: Thoroughly analyze the SimulationCraft APL to understand:
   - Spell priority order and conditions
   - Resource management logic
   - Cooldown and talent-based conditionals
   - AoE and single-target rotation differences
   - Defensive and utility spell usage

2. **Framework Mapping**: Map APL actions to Makulu Framework equivalents:
   - Convert APL spell names to proper WoW API spell IDs
   - Translate APL conditions to Makulu unit/buff/debuff checks
   - Implement proper callback structures for each action
   - Use appropriate Makulu caching for performance

3. **Code Generation**: Generate clean, maintainable LUA code that:
   - Follows established Makulu Framework patterns
   - Uses proper spell definitions with MAKULU_INFO attributes
   - Implements efficient game state management
   - Includes comprehensive callback functions
   - Avoid writing trinket/item logic, it is handled by the framework.

4. **Validation**: Ensure the converted rotation:
   - Compiles without syntax errors
   - Follows all Makulu Framework conventions
   - Maintains logical flow equivalent to original APL
   - Handles all edge cases appropriately

### Quality Standards
- **Accuracy**: 100% faithful translation of APL logic
- **Performance**: Optimized for minimal CPU usage during combat
- **Maintainability**: Clean, well-commented, readable code
- **Compatibility**: Works with all Makulu Framework features
- **Completeness**: Includes all APL actions and conditions

## File Structure and Organization

### Rotation Files Location
- Main rotation files: `/Rotations/CLASS_NAME/ClassName_Spec.lua`
- Ignore these directories:
  - `#BerserkersStuff`
  - `#General`
  - `MOP`
  - `MakuluFramework`

### File Naming Convention
- `CLASSNAME_SPEC.lua` (e.g., `Warrior_Arms.lua`, `Mage_Fire.lua`)
- There is a few files that do not follow this convention.

## Spell Definition Structure

### ActionID Table Format
```lua
local ActionID = {
    SpellName = { 
        ID = spellIdNumber, 
        MAKULU_INFO = { 
            damageType = "physical" or "magic",
            offGcd = true, -- if off global cooldown
            ignoreCasting = true, -- if can be used while casting
            ignoreMoving = true, -- if can be used while moving
            cc = true -- if crowd control spell
        } 
    },
    -- Hidden talents for conditional checks
    TalentName = { ID = talentId, Hidden = true },
}
```

### Spell Properties
When defining spells, use these common MAKULU_INFO properties:
- `damageType`: "physical" or "magic" (for immunity checks)
- `offGcd`: true if the spell is off the global cooldown
- `ignoreCasting`: true if it can be used while casting another spell
- `ignoreMoving`: true if it can be used while moving
- `cc`: true if it's a crowd control ability
- `heal`: true if it's a healing spell

## Callback System Usage

### Standard Callback Pattern
```lua
SpellName:Callback("condition", function(spell)
    if conditionCheck() then
        return spell:Cast(target)
    end
end)
```

### Common Callback Keys
- `"main"` - Primary usage condition
- `"cooldown"` - Cooldown management
- `"defensive"` - Defensive cooldowns
- `"aoe"` - Area of effect conditions
- `"precombat"` - Pre-combat actions
- `"interrupt"` - Interrupt conditions

### Running Callbacks in A[3]
```lua
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()
    
    -- Run callbacks in priority order
    SpellName("condition1")
    SpellName("condition2")
    
    return FrameworkEnd()
end
```

## Rotation Structure in A[3]

### Standard A[3] Function
```lua
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if Action.Zone == "arena" then
        FakeCasting.gglFakeCast(icon)
    end

    if A.GetToggle(2, "makDebug") then
        -- Debug information display
    end

    if player.channeling then return end
    
    makInterrupt(interrupts)
    
    -- Pre-combat actions
    SpellName("precombat")
    
    if target.exists and target.canAttack and Spell:InRange(target) then
        -- In-combat rotation
        CooldownSpell("cooldown")
        DefensiveSpell("defensive")
        RotationSpell("main")
    end

    return FrameworkEnd()
end
```

### Game State Management
Always use a game state table to cache expensive calculations:

```lua
local gameState = {
    shouldAoE = false,
    activeEnemies = 0,
    -- other state variables
}

local function updateGameState()
    gameState.shouldAoE = activeEnemies() >= 3 and A.GetToggle(2, "AoE")
    gameState.activeEnemies = activeEnemies()
    -- update other state variables
end
```

## Buff and Debuff Management

### Definition Structure
```lua
local buffs = {
    buffName = spellId,
    -- e.g., tigersFury = 5217
}

local debuffs = {
    debuffName = spellId,
    -- e.g., rip = 1079
}
```

### Common Checks
```lua
-- Check if player has buff
if player:Buff(buffs.tigersFury) then

-- Check if target has debuff
if target:Debuff(debuffs.rip) then

-- Check debuff remaining time
if target:DebuffRemains(debuffs.rip) < 5000 then

-- Check if debuff is refreshable (within 30% of duration)
if target:DebuffPandemic(debuffs.rip) then
```

## Interrupt System

### Interrupt Definition
```lua
local interrupts = {
    { spell = InterruptSpellName },
    { spell = CrowdControlSpell, isCC = true, aoe = true, distance = 8 }
}
```

### Usage in Rotation
```lua
if not player.channeling then
    makInterrupt(interrupts)
end
```

## Caching System

### Cache Types
1. **Frame Cache**: `cacheContext:getCell()` - Reset every frame
2. **Constant Cache**: `cacheContext:getConstCacheCell()` - Persists between frames
3. **Combat Cache**: `cacheContext:getCombatCacheCell()` - Reset when leaving combat

### Usage Pattern
```lua
local constCell = cacheContext:getConstCacheCell()

local function expensiveCalculation()
    return constCell:GetOrSet("cacheKey", function()
        -- expensive operation here
        return result
    end)
end
```

## Multi-Unit Management

### Accessing Unit Groups
```lua
local party = MakMulti.party
local arena = MakMulti.arena
local enemies = MakMulti.enemies
```

### Common Operations
```lua
-- Count units meeting condition
local count = party:Count(function(unit) 
    return unit.hp < 50 
end)

-- Find unit with lowest value
local lowest = party:Lowest(function(unit) 
    return unit.hp 
end)

-- Check if any unit meets condition
local any = enemies:Any(function(unit) 
    return unit:IsCasting() 
end)
```

## Defensive Cooldown Management

### Standard Defensive Pattern
```lua
DefensiveSpell:Callback("defensive", function(spell)
    if shouldDefensive() then 
        return spell:Cast(player)
    end
    
    if player.hp < A.GetToggle(2, "DefensiveSpellSlider") then
        return spell:Cast(player)
    end
end)
```

### Helper Functions
```lua
local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    return player:BuffFrom(MakLists.Defensive) or 
           UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()
    return incomingDamage and not defensiveActive()
end
```

## Talent Condition Checks

### Hidden Talent Definitions
```lua
local ActionID = {
    TalentName = { ID = talentId, Hidden = true },
}
```

### Usage
```lua
if A.TalentName:IsTalentLearned() then
    -- talent-specific logic
end

-- Or using the older method
if IsPlayerSpell(A.TalentName.ID) then
    -- talent-specific logic
end
```

## Utility Functions

### Common Utility Patterns
```lua
-- Number conversion for boolean checks
local function num(val)
    if val then return 1 else return 0 end
end

-- Range checking
if Spell:InRange(target) then

-- Facing checks (usually handled by framework)
if target.los then

-- Movement checks
if not player.moving then

-- Resource checks
if player.energy > 50 then
```

## Debugging and Awareness

### Debug Display
```lua
if A.GetToggle(2, "makDebug") then
    MakPrint(1, "Debug Message: ", variable)
end
```

### UI Awareness
```lua
local awareAlert = A.GetToggle(2, "makAware")
if awareAlert[1] then
    Aware:displayMessage("Message Text", "Color", 1)
end
```

## Common Mistakes to Avoid

1. **Never modify framework files** - Only work in rotation files
2. **Don't forget FrameworkStart/End** - Always wrap A[3] with these
3. **Proper spell casting** - Always use `spell:Cast(target)` not direct calls
4. **Range checking** - Always verify spell is in range before casting
5. **Resource management** - Check resources before suggesting resource-consuming spells
6. **Caching expensive operations** - Don't repeat expensive calculations
7. **Unit existence checks** - Always verify units exist before accessing properties

## MakuluFramework Reference Files

The following files in `/Rotations/MakuluFramework/` are available for reference but should NOT be modified:

### Core Framework Components (Read-Only)

1. **Unit System** (`Unit.lua`, `ConstUnits.lua`)
   - Provides unit tracking and management
   - Pre-defined constant units: `player`, `target`, `focus`, `mouseover`, `party1-4`, `arena1-3`, `healer`, `enemyHealer`, `tank`, `pet`
   - Methods for health, resources, buffs/debuffs, combat state, movement, casting info

2. **Spell System** (`Spell.lua`)
   - Spell casting, cooldown, and state management
   - Constant spells available: `TabTarget`, `TargetMouseOver`, `TargetLastTarget`, `TargetArena1-5`, `FocusArena1-5`, `FocusParty1-4`, `FocusPlayer`, `StopCasting`, `Trinket1`, `Trinket2`, `HealthStone`, `HealthPotion`, `Potion`, `PoolResources`
   - Callback system for conditional spell usage

3. **Cache System** (`Cache.lua`)
   - Performance optimization through intelligent caching
   - Cache types: Frame (`getCell`), Constant (`getConstCacheCell`), Combat (`getCombatCacheCell`)

4. **MultiUnits System** (`MultiUnits.lua`)
   - Group operations for party, arena, enemy units
   - Methods: `ForEach`, `Any`, `Find`, `Filter`, `Size`, `Highest`, `Lowest`, `Sum`, `Average`, `Count`

5. **Utility Functions** (`Utils.lua`)
   - Helper functions including `debounce` and `debounceSpell`
   - PvP instance detection, string manipulation, table conversion

6. **Predefined Lists** (`Lists.lua`)
   - Collections of spell IDs for various purposes
   - Examples: `purgeableBuffs`, `shatteringBuffs`, `feared`, `arenaDispelDebuffs`, `arenaKicks`, `arenaSpellReflect`, `Defensive`

7. **Boss Mods Integration** (`BossMods.lua`)
   - Integration with DBM and BigWigs for boss mechanic timers
   - Methods for defensive cooldown timing and incoming damage prediction

8. **Damage Tracking** (`DamageTracker.lua`)
   - Time-to-die estimation and damage per second calculations

9. **Other Modules**:
   - `ArenaState.lua` - Arena-specific functionality
   - `Immunities.lua` - Unit immunity tracking
   - `Item.lua` - Item management
   - `Sequence.lua` - Spell sequence handling

### Usage Guidelines for Framework Files

- **DO**: Reference these files to understand available methods and functionality
- **DO**: Use the provided utilities, unit methods, and spell management
- **DO NOT**: Modify any files in the MakuluFramework directory
- **DO NOT**: Redefine framework functions or constants
- **DO**: Follow the established patterns for callbacks, caching, and unit management

## Performance Guidelines

1. **Cache expensive calculations** in the `updateGameState()` function
2. **Use appropriate cache types** (frame vs const vs combat)
3. **Minimize calls to WoW API** functions in hot paths
4. **Batch unit operations** using MultiUnits when possible
5. **Avoid string operations** in frequently called functions

## Testing Checklist

Before committing changes, verify:
- [ ] Spell IDs are correct and match current game version
- [ ] All callbacks follow the established pattern
- [ ] Caching is used appropriately for expensive operations
- [ ] Unit existence checks are performed before accessing properties
- [ ] Spell range checks are implemented where needed
- [ ] Resource management is properly handled
- [ ] Defensive cooldown logic is sound
- [ ] AOE detection works correctly
- [ ] Interrupt system functions properly
- [ ] No syntax errors or framework violations
- [ ] Debug output (if any) is removed or properly conditional