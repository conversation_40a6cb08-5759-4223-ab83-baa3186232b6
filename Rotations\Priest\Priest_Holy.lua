-- For Later (Check if spec in group)
-- MultiUnits.party:Any(function(unit) return unit.spec == assaSpecId)


if not MakuluValidCheck() then return true end
if not Makulu_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 257 then return end

local FrameworkStart   = MakuluFramwork.start
local FrameworkEnd     = MakuluFramwork.endFunc
local RegisterIcon     = MakuluFramwork.registerIcon

local MakUnit          = MakuluFramwork.Unit
local MakSpell         = MakuluFramwork.Spell
local MakMulti         = MakuluFramwork.MultiUnits
local TableToLocal     = MakuluFramwork.tableToLocal
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local Debounce         = MakuluFramework.debounceSpell
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local Unit       	   = Action.Unit
local Player           = Action.Player
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local AuraIsValid      = Action.AuraIsValid
local UnitIsUnit	   = _G.UnitIsUnit
local HealingEngine    = Action.HealingEngine
local getmembersAll    = HealingEngine.GetMembersAll()
local _G, setmetatable = _G, setmetatable
local GetSpellTexture  = _G.TMW.GetSpellTexture
local MakLists         = MakuluFramework.lists

local ActionID = {
    WillToSurvive 				            = { ID = 59752 	},
    Stoneform 					            = { ID = 20594 	},
    Shadowmeld 					            = { ID = 58984 	},
    EscapeArtist 				            = { ID = 20589 	},
    GiftOfTheNaaru  			            = { ID = 59544 	},
    Darkflight 					            = { ID = 68992 	},
    BloodFury 					            = { ID = 20572 	},
    WillOfTheForsaken 			            = { ID = 7744 	},
    WarStomp 					            = { ID = 20549 	},
    Berserking 					            = { ID = 26297 	},
    ArcaneTorrent 				            = { ID = 50613 	},
    RocketJump 					            = { ID = 69070 	},
    RocketBarrage				            = { ID = 69041	},
    QuakingPalm 				            = { ID = 107079 },
    SpatialRift 				            = { ID = 256948 },
    LightsJudgment 				            = { ID = 255647 },
    Fireblood 					            = { ID = 265221 },
    ArcanePulse 				            = { ID = 260364 },
    BullRush 					            = { ID = 255654 },
    AncestralCall 				            = { ID = 274738 },
    Haymaker 					            = { ID = 287712 },
    Regeneratin 				            = { ID = 291944 },
    BagOfTricks 				            = { ID = 312411 },
    HyperOrganicLightOriginator             = { ID = 312924 },
	TargetEnemy 				            = { ID = 44603  },
	StopCast 					            = { ID = 61721  },
	PoolResource 				            = { ID = 209274 },
	FocusParty1 				            = { ID = 134314 },
	FocusParty2 				            = { ID = 134316 },
	FocusParty3 				            = { ID = 134318 },
	FocusParty4 				            = { ID = 134320 },
	FocusPlayer 				            = { ID = 134310 },
	AntiFakeKick                            = { Type = "SpellSingleColor", ID = 88625,  Hidden = true,		Color = "RED"	    , Desc = "[2] AntiFakeKick",    QueueForbidden = true	},
	AntiFakeCC					            = { Type = "SpellSingleColor", ID = 88625,  	Hidden = true,  Color = "RED"	    , Desc = "[1] AntiFakeCC",      QueueForbidden = true	},

    --Priest General
	DesperatePrayer                       = { ID = 19236     },
	Fade                                  = { ID = 586       },
	FlashHeal                             = { ID = 2061, Macro = "/cast [@target,help][@focus,help]spell:thisID"     },
    Heal                                  = { ID = 2060, Macro = "/cast [@target,help][@focus,help]spell:thisID"      },
	--FlashHeal                             = { ID = 2061, offGcd = true, ignoreCasting = true      },
    --Heal                                  = { ID = 2060, offGcd = true, ignoreCasting = true      },
	MindBlast                             = { ID = 8092, MAKULU_INFO = { damageType = "magic" }   },
	PowerWordFortitude                    = { ID = 21562, Macro = "/cast [@player]spell:thisID"     },
	PowerWordShield                       = { ID = 17, Macro = "/cast [@target,help][@focus,help]spell:thisID"        },
	PsychicScream                         = { ID = 8122      },
	Resurrection                          = { ID = 2006, Macro = "/cast [@target,help][@focus,help]spell:thisID"      },
	ShadowWordPain                        = { ID = 589       },
	Smite                                 = { ID = 585, MAKULU_INFO = { damageType = "magic" }       },
	Levitate                              = { ID = 1706      },
	MindVision                            = { ID = 2096      },
	MindSoothe                            = { ID = 453       },
	Renew                                 = { ID = 139, Macro = "/cast [@target,help][@focus,help]spell:thisID"       },
	DispelMagic                           = { ID = 528, MAKULU_INFO = { damageType = "magic" }        },
	Shadowfiend                           = { ID = 34433	 },
	PrayerOfMending                       = { ID = 33076, Macro = "/cast [@target,help][@focus,help]spell:thisID"     },
	PrayerOfMendingBuff                   = { ID = 41635, Hidden = true		},
	ShadowWordDeath                       = { ID = 32379     },
	HolyNova                              = { ID = 132157    },
	AngelicFeather                        = { ID = 121536, Macro = "/cast [@player]spell:thisID"    },
	LeapofFaith                           = { ID = 73325     },
	ShackleUndead                         = { ID = 9484      },
	DominateMind                          = { ID = 205364    },
	MindControl                           = { ID = 605       },
	VoidTendrils                          = { ID = 108920    },
	MassDispel                            = { ID = 32375     },
	PowerInfusion                         = { ID = 10060     },
	VampiricEmbrace                       = { ID = 15286     },
	DivineStar                            = { ID = 110744    },
	Halo                                  = { ID = 120517    },
	Mindgames                             = { ID = 375901    },
    VoidShift = { ID = 108968, MAKULU_INFO = { ignoreCasting = true } },
	PowerWordLife                         = { ID = 373481, Macro = "/cast [@target,help][@focus,help]spell:thisID"	 },
    PowerWordLifeToo                      = { ID = 440678, Macro = "/cast [@target,help][@focus,help]Power Word: Life"	 },
    PowerInfusionP                        = { ID = 10060, Texture = 316262, Hidden = true, MAKULU_INFO = { ignoreCasting = true }     },

    -- Holy Specific
	Apotheosis                            = { ID = 200183    },
	DivineWord                            = { ID = 372760	 },
	EmpyrealBlaze                         = { ID = 372616    },
	EmpyrealBlazeBuff                     = { ID = 372617, Hidden = true	},
	Lightwell                             = { ID = 372835    },
	Purify                                = { ID = 527, Macro = "/cast [@target,help][@focus,help]spell:thisID"       },
	HolyWordSerenity                      = { ID = 2050, Macro = "/cast [@target,help][@focus,help]spell:thisID"      },
	HolyFire                              = { ID = 14914, MAKULU_INFO = { damageType = "magic" }     },
	HolyWordChastise                      = { ID = 88625     },
	HolyWordSalvation                     = { ID = 265202    },
	GuardianSpirit                        = { ID = 47788, MAKULU_INFO = { offGcd = true, ignoreCasting = true }, Macro = "/cast [@target,help][@focus,help]spell:thisID" },
	PrayerofHealing                       = { ID = 596, Macro = "/cast [@target,help][@focus,help]spell:thisID"       },
	MassResurrection                      = { ID = 212036    },
	HolyWordSanctify                      = { ID = 34861, Macro = "/cast [@player]spell:thisID"     },
    DivineHymn                            = { ID = 64843     },
	SymbolofHope                          = { ID = 64901     },
	LightweaverTalent                     = { ID = 390992, Hidden = true    },
    LightweaverBuff                       = { ID = 390993, Hidden = true    },

    GreaterHeal                           = { ID = 289666,   },
    SurgeofLight                          = { ID = 109186	},
    SurgeofLightBuff                      = { ID = 114255	},
    PrayerCircle                          = { ID = 321377	},

    -- Hero
    MiraculousRecovery                    = { ID = 440674, Hidden = true  },

    -- PvP Talents
    HolyWard                              = { ID = 213610    },
    HolyWordConcentration                 = { ID = 289657    },
    SpiritoftheRedeemer                   = { ID = 215769    },
    RayofHope                             = { ID = 197268    },
    GreaterFade                           = { ID = 213602    },
    CircleofHealing                       = { ID = 204883, Macro = "/cast [@target,help][@focus,help]spell:thisID"    },
    Thoughtsteal                          = { ID = 316262    },
    DivineAscension                       = { ID = 328530    },
	DivineImage                           = { ID = 405963, Hidden = true 	},
	SpiritofRedemption                    = { ID = 27827, Hidden = true 	},
	ResonantWords					  	  = { ID = 372313, Hidden = true 	},
	PhaseShift					  	      = { ID = 408557 	},
	Epiphany					  	      = { ID = 414553 	},

	--Improved Dispel
	ImprovedDispelTalent                  = { ID = 390632, Hidden = true  },

    PremonitionBundle                     = { ID = 428924 },
    TranslucentImage   		  	          = { ID = 373446, Hidden = true 	},
    EternalSanctity   		  	          = { ID = 1215245, Hidden = true 	},
    EmpoweredRenew   		  	          = { ID = 391339, Hidden = true 	},

    --Infa adds for Arena
    PremonitionofInsightArena = { ID = 428933, FixedTexture = 5927640, Hidde = true, MAKULU_INFO = { ignoreCasting = true } }, 
    PremonitionofPietyArena = { ID = 428930, FixedTexture = 5927640, Hidde = true, MAKULU_INFO = { ignoreCasting = true } },
    PremonitionofSolaceArena = { ID = 428934, FixedTexture = 5927640, Hidde = true, MAKULU_INFO = { ignoreCasting = true }  },
    PremonitionofClairvoyanceArena = { ID = 440725, FixedTexture = 5927640, Hidde = true, MAKULU_INFO = { ignoreCasting = true }  },
}

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local A, M = MakuluFramework.CreateActionVar(ActionID, true)
A = setmetatable(A, { __index = Action })

Action[ACTION_CONST_PRIEST_HOLY] = A
TableToLocal(M, getfenv(1))

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local player        = ConstUnit.player
local target        = ConstUnit.target
local focus         = ConstUnit.focus
local mouseover     = ConstUnit.mouseover
local arena1        = ConstUnit.arena1
local arena2        = ConstUnit.arena2
local arena3        = ConstUnit.arena3
local party1        = ConstUnit.party1
local party2        = ConstUnit.party2
local party3        = ConstUnit.party3
local party4        = ConstUnit.party4
local tank          = ConstUnit.tank
local healer        = ConstUnit.healer
local enemyHealer   = ConstUnit.enemyHealer

local unit

Aware:enable()

local buffs = {
    empyrealblaze = 372617,
    feather = 121557,
    apotheosis = 200183,
    insurance = 1215349,
    answeredPrayers = 394289,
    spiritOfRedemption = 215982,
    spiritOfTheRedeemer = 215769,
    prayerOfMending = 41635,
    renew = 139,
}

local debuffs = {

}

local gameState = {
    imCasting = nil,
    imCastingName = nil,
    imCastingRemaining = nil,
    imCastingLength = nil,
}

local function getBelowHP(percent)
    return MakMulti.party:Count(function(units)
        return FlashHeal:InRange(units) and units.hp < percent and units.hp > 0
    end)
end

local function enemyBurstCount()
    local burstCount = 0

    if arena1.exists and arena1.cds then burstCount = burstCount + 1 end
    if arena2.exists and arena2.cds then burstCount = burstCount + 1 end
    if arena3.exists and arena3.cds then burstCount = burstCount + 1 end

    return burstCount
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function handleInterrupts(enemiesList)
    local _, _, _, lagWorld = GetNetStats()
    local interruptSpells = {
        {spell = A.HolyWordChastise, minPercent = 50, isCC = true},
    }

    if A.Zone == "arena" or A.Zone == "pvp" then return end

    local activeEnemies = MultiUnits:GetActiveUnitPlates()
    for enemyGUID in pairs(activeEnemies) do
        local enemy = MakUnit:new(enemyGUID)
        for _, spellInfo in ipairs(interruptSpells) do
            local shouldInterrupt = enemy:PvEInterrupt(spellInfo.spell, spellInfo.minPercent, spellInfo.minEndTime, spellInfo.isCC, spellInfo.aoe)
            if shouldInterrupt == "Switch" and not spellInfo.aoe then
                Aware:displayMessage("Switching to " .. enemy.name .. " to interrupt", "White", 2.5)
                return "Switch"
            elseif shouldInterrupt == true then
                Aware:displayMessage("Interrupting " .. enemy.name, "White", 2.5)
                return spellInfo.spell
            end
        end
    end

    return nil
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function hasIncomingDamage()
    return incBigDmgIn() <= 2000 or incModDmgIn() <= 2000
end

local function defensiveActive()
    player = MakUnit:new("player")
    return player:HasBuff(buffs.barkskin, true) or player:HasBuff(buffs.ironbark, true)
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive()
end

local function CanAttackTarget()
    return target.exists and not target.isFriendly and target.canAttack
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

TMW:RegisterCallback("TMW_ACTION_HEALINGENGINE_UNIT_UPDATE", function(_, thisUnit, db, QueueOrder)

    local unitID = thisUnit.Unit
    local Role = thisUnit.Role

    if Action.Zone == "arena" then
        local unitA = MakUnit:new(unitID)

        --Spirit of Redemption / Spirit of the Redeemer
        if unitA:Buff(buffs.spiritOfRedemption) or unitA:Buff(buffs.spiritOfTheRedeemer) then
            thisUnit.Enabled = false
        else 
            thisUnit.Enabled = true
        end
    end

    if Action.Zone == "arena" or thisUnit.realHP < 35 then return end

    -- Check if 'HE' should not be used based on certain conditions
    if thisUnit.Enabled then 
        local unit = Unit(unitID)
        
        -- Condition: Player is mounted
        local isPlayerMounted = Player:IsMounted()
        
        -- Condition: Unit is out of range (> 40 yards)
        local isUnitOutOfRange = unit:GetRange() > 40
        
        -- Buff IDs for Spirit of Redemption and Spirit of Redeemer
        local spiritOfRedemptionBuffID = 27827  -- Spirit of Redemption
        local spiritOfRedeemerBuffID = 215769   -- Spirit of Redeemer
        
        -- Condition: Unit has Spirit of Redemption or Spirit of Redeemer buff
        local unitHasSpiritBuff = unit:IsBuffUp(spiritOfRedemptionBuffID) or unit:IsBuffUp(spiritOfRedeemerBuffID)
        
        -- If any condition is true, disable 'thisUnit'
        if isPlayerMounted or unitHasSpiritBuff then --
            thisUnit.Enabled = false
        end
    end

    local offset, hpReduction
    if thisUnit.isSelf then
        offset, hpReduction = db.OffsetSelfDispel, 80
    elseif Role == "HEALER" then
        offset, hpReduction = db.OffsetHealersDispel, 70
    elseif Role == "TANK" then
        offset, hpReduction = db.OffsetTanksDispel, 50
    else
        offset, hpReduction = db.OffsetDamagersDispel, 60
    end

    thisUnit:SetupOffsets(offset, thisUnit.realHP - hpReduction)
end)

--######################################################################################################################################################################################################

local function getCurrentCastInfo()
    local castingInfo = player.castOrChannelInfo

    if not castingInfo then
        return nil, nil, nil, nil
    end

    return castingInfo.spellId, castingInfo.name, castingInfo.remaining, castingInfo.castLength
end

local lastUpdateTime = 0
local updateDelay = 0.5

-- Funktion zur Aktualisierung des Spielzustands
local function updateGameState()
    local currentTime = GetTime()

    local currentCast, currentCastName, currentCastRemains, currentCastLength = getCurrentCastInfo()
    gameState.imCastingRemaining = currentCastRemains

    if (currentTime - lastUpdateTime) > updateDelay then
        gameState.imCasting = currentCast
        gameState.imCastingName = currentCastName
        lastUpdateTime = currentTime 
    end
end

--################################################################################################################################################################################################################

local function CanPowerInfusion()
    if combatTime == 0 then return false end

    local pitarget = GetToggle(2, "PowerInfusionDropdown")
    local partyUnits = { "party1", "party2", "party3", "party4" }

    for i = 1, #getmembersAll do
        local unitID = getmembersAll[i].Unit
        local isReady = A.PowerInfusion:IsReadyByPassCastGCD(unitID)
        local hasDamageBuff = Unit(unitID):IsBuffUp(PiTable)
        local hasNoPiDebuff = Unit(unitID):IsBuffDown(A.PowerInfusion.ID, true)
        local isNotTank = not Unit(unitID):Role("TANK")
        local canPI = isReady and hasNoPiDebuff and hasDamageBuff and isNotTank

        if pitarget == "All" or pitarget == unitID then
            for _, partyUnit in ipairs(partyUnits) do
                if UnitIsUnit(unitID, partyUnit) and canPI then
                    HealingEngine.SetTarget(unitID)
                    return true
                end
            end
        end
    end

    return false
end

--################################################################################################################################################################################################################

local function holdGCDforSWDP()
    if Action.Zone ~= "arena" then return false end
    if ShadowWordDeath:Cooldown() > 1000 then return false end
    if getBelowHP(30) > 0 then return false end
    if arena1.exists and arena1:CastingFromFor(MakLists.swdList, 1) and not arena1:CastingFromFor(MakLists.swdList, 420) then return true end
    if arena2.exists and arena2:CastingFromFor(MakLists.swdList, 1) and not arena2:CastingFromFor(MakLists.swdList, 420) then return true end
    if arena3.exists and arena3:CastingFromFor(MakLists.swdList, 1) and not arena3:CastingFromFor(MakLists.swdList, 420) then return true end
    return false
end

local function holdGCDforSWD()
    if Action.Zone ~= "arena" then return false end
    if ShadowWordDeath.cd > 1000 then return false end
    if getBelowHP(30) > 0 then return false end
    if arena1.exists and arena1:CastingFromFor(MakLists.swdList, 1) --[[and not arena1:CastingFromFor(MakLists.swdList, 420)]] then return true end
    if arena2.exists and arena2:CastingFromFor(MakLists.swdList, 1) --[[and not arena2:CastingFromFor(MakLists.swdList, 420)]] then return true end
    if arena3.exists and arena3:CastingFromFor(MakLists.swdList, 1) --[[and not arena3:CastingFromFor(MakLists.swdList, 420)]] then return true end
    return false
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### UTILITIES ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- AngelicFeather
AngelicFeather:Callback('Movement', function(spell)
    if Action.Zone == "arena" then return end
    if player:HasBuff(buffs.feather) then return end
    if isStaying then return end

    return spell:Cast(player)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### DEFENSIVE ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- DesperatePrayer
DesperatePrayer:Callback('Defensive', function(spell)
    if playerHealth > GetToggle(2, "SelfProtection1") then return end

    return spell:Cast(player)
end)

-- Fade
Fade:Callback('Defensive', function(spell)
    if Action.Zone == "arena" then return end
    if not player:TalentKnown(TranslucentImage.id) then return end
    if not shouldDefensive() then return end

    return spell:Cast()
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### PRE COMBAT ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- PowerWordFortitude
--PowerWordFortitude:Callback('PreCombat', function(spell)
--    if player:HasBuff(A.PowerWordFortitude.ID) then return end
--
--    return spell:Cast(player)
--end)

PowerWordFortitude:Callback('PreCombat', function(spell)
    if player.inCombat then return end

    if Action.Zone == "arena" and player.combatTime > 0 then return end

    local missingBuff = MakMulti.party:Any(function(unit) return not unit:Buff(PowerWordFortitude.wowName) and unit.distance < 40 and not unit.isPet and unit.hp > 0 end)
    local outOfRange = MakMulti.party:Any(function(unit) return unit.distance >= 40 or C_Map.GetBestMapForUnit(player:CallerId()) ~= C_Map.GetBestMapForUnit(unit:CallerId()) end)
    
    if MakMulti.party:Size() <= 5 and outOfRange then return end -- attempt to wait for everyone to join the instance, dungeon only

    if missingBuff then 
        return Debounce("pwf", 1000, 2500, spell, player)
    end
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### DISPEL ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

--Purify
Purify:Callback('Dispel', function(spell)
    if A.Zone == "arena" then return end
    if unit.ehp < 35 then return end
    if unit:DebuffFrom(AvoidDispelTable) then return end

    local magicked  = MakMulti.party:Find(function(unit) return (unit.magicked or AuraIsValid(unit:CallerId(), "UseDispel", "Magic")) and Purify:InRange(unit) end)
    local diseased  = MakMulti.party:Find(function(unit) return (unit.cursed or AuraIsValid(unit:CallerId(), "UseDispel", "Disease")) and Purify:InRange(unit) end)

    if magicked then
        HealingEngine.SetTarget(magicked:CallerId(), 1)
        Debounce("dispel", 1000, 2500, spell, unit)
    end

    if diseased and player:TalentKnown(ImprovedDispelTalent.id) then
        HealingEngine.SetTarget(diseased:CallerId(), 1)
        Debounce("dispel", 1000, 2500, spell, unit)
    end
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### EMERGENCY SINGLE TARGET COOLDOWN ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- VoidShift
VoidShift:Callback('Emergency', function(spell)
    if combatTime == 0 then return end
    if unit.hp > 35 then return end
    if player.hp < 60 then return end
    if not unit:IsPlayer() then return end

    return spell:Cast(unit)
end)

-- GuardianSpirit
GuardianSpirit:Callback('Emergency', function(spell)
    if combatTime == 0 or unit.hp > 35 then return end
    if not unit:IsPlayer() then return end

    if GetToggle(2, "GSTank") then
        if not unit.isTank then return end
    end

    return spell:Cast(unit)
end)

-- PremonitionBundle
PremonitionBundle:Callback('HealingPve', function(spell)
    return spell:Cast(player)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### RACIALS ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function CanUseRacialAbility()
    return A.GetToggle(1, "Racial") and CanUseHealingCooldowns()
end

local function UseRacialAbility(spell)
    if not CanUseRacialAbility() then
        return false
    end
    return spell:Cast(player)
end

-- BloodFury
BloodFury:Callback('Racials', function(spell)
    return UseRacialAbility(spell)
end)

-- Berserking
Berserking:Callback('Racials', function(spell)
    return UseRacialAbility(spell)
end)

-- Fireblood
Fireblood:Callback('Racials', function(spell)
    return UseRacialAbility(spell)
end)

-- AncestralCall
AncestralCall:Callback('Racials', function(spell)
    return UseRacialAbility(spell)
end)

-- BagOfTricks
BagOfTricks:Callback('Racials', function(spell)
    return UseRacialAbility(spell)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### COOLDOWNS ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function MajorCooldownIsActive()
    return not player:Buff(A.Apotheosis.ID, true) and not player:Buff(A.DivineHymn.ID, true) and not player:Buff(A.SpiritofRedemption.ID, true) and A.HolyWordSalvation:GetSpellTimeSinceLastCast() > 15
end

local function HandleHealingCooldowns(spell, allowMoving)
    if not allowMoving and isMoving then return end
    if MajorCooldownIsActive() then return end
    if not CanUseHealingCooldowns() then return end

    return spell:Cast(player)
end

-- HolyWordSalvation
HolyWordSalvation:Callback('Cooldowns', function(spell)
    return HandleHealingCooldowns(spell, false)
end)

-- DivineHymn
DivineHymn:Callback('Cooldowns', function(spell)
    return HandleHealingCooldowns(spell, false)
end)

-- Apotheosis
Apotheosis:Callback('Cooldowns', function(spell)
    return HandleHealingCooldowns(spell, true)
end)

-- PowerInfusion
PowerInfusion:Callback('Cooldowns', function(spell)
    if not CanPowerInfusion() then return end
    if unit.hp < 45 then return end

    return spell:Cast(unit)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### STANDARD HEALING PVE ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- PowerWordLife
PowerWordLife:Callback('HealingPve', function(spell)
    if player:TalentKnown(MiraculousRecovery.id) then return end
    if unit.hp > 35 then return end

    return spell:Cast(unit)
end)

PowerWordLifeToo:Callback('HealingPve', function(spell)
    if not player:TalentKnown(MiraculousRecovery.id) then return end
    if unit.hp > 50 then return end

    return spell:Cast(unit)
end)

-- FlashHeal
FlashHeal:Callback('Freecast', function(spell)
    if not player:Buff(114255) then return end
    if not AutoHeal(unit:CallerId(), A.FlashHeal) then return end

    return spell:Cast(unit)
end)

-- DivineWord
DivineWord:Callback('HealingPve', function(spell)
    local serenitySlider = GetToggle(2, "HolyWordSerenitySlider")
    if not AutoHealOrSlider(unit:CallerId(), A.HolyWordSerenity, serenitySlider) then return end

    return spell:Cast(player)
end)

local function AreBuffsValid()
    local isDivineImageEnabled = GetToggle(2, "DivineImageBox")
    local isResonantWordsEnabled = GetToggle(2, "ResonantWordsBox")
    
    -- Buffs sind gültig, wenn die Checkbox deaktiviert ist oder der Buff nicht aktiv ist
    local isDivineImageValid = not isDivineImageEnabled or not player:Buff(A.DivineImage.ID)
    local isResonantWordsValid = not isResonantWordsEnabled or not player:Buff(A.ResonantWords.ID)
    
    -- Beide Buff-Checks müssen erfüllt sein, damit der Zauber verwendet werden kann
    return isDivineImageValid and isResonantWordsValid
end

-- HolyWordSanctify
HolyWordSanctify:Callback('HealingPve', function(spell)
    -- Überprüfen, ob die Buff-Bedingungen und die AoE-Heilung gültig sind
    if not AreBuffsValid() or not CanUseAoeHealing() then return end
    
    if player:TalentKnown(EternalSanctity.id) then
        if Apotheosis.cd < spell:TimeToFullCharges() and not player:Buff(buffs.apotheosis) then
            if spell.charges < spell.maxCharges then return end
        end
    end

    if player:HasBuffCount(buffs.answeredPrayers) >= 40 and not player:Buff(buffs.apotheosis) then
        if spell.charges < spell.maxCharges then return end
    end

    return spell:Cast(unit)
end)

-- HolyWordSerenity
HolyWordSerenity:Callback('HealingPve', function(spell)
    -- Überprüfen, ob die Buff-Bedingungen gültig sind
    if not AreBuffsValid() then return end

    if player:TalentKnown(EternalSanctity.id) then
        if Apotheosis.cd < spell:TimeToFullCharges() and not player:Buff(buffs.apotheosis) then
            if spell.charges < spell.maxCharges and unit.hp > 30 then return end
        end
    end

    if player:HasBuffCount(buffs.answeredPrayers) >= 40 and not player:Buff(buffs.apotheosis) then
        if spell.charges < spell.maxCharges then return end
    end

    if A.PremonitionBundle:IsReadyByPassCastGCD() and getBelowHP(90) >= 1 then
        return PremonitionBundle('HealingPve') 
    end

    local serenityThreshold = GetToggle(2, "HolyWordSerenitySlider") + (num(unit:Buff(buffs.insurance, true)) * 25)
    
    local insuranceUnit = MakMulti.party:Lowest(
        function(friendly) return friendly.ehp end,
        function(friendly) return HolyWordSerenity:InRange(friendly) and friendly.hp > 0 and friendly.ehp < GetToggle(2, "HolyWordSerenitySlider") + (num(friendly:Buff(buffs.insurance, true)) * 25) end
    )

    if insuranceUnit then
        if unit:BuffRemains(buffs.insurance, true) > 1000 and unit.ehp > serenityThreshold then
            HealingEngine.SetTarget(insuranceUnit:CallerId(), 1)
        end
    end

    if AutoHealOrSlider(unitID, A.HolyWordSerenity, serenityThreshold) then
        return spell:Cast(unit)
    end
end)

-- PrayerOfMending
PrayerOfMending:Callback('HighPrio', function(spell)
    if GetToggle(2, "PrayerofMendingMenu") ~= "1" then return end
    if unit:Buff(A.PrayerOfMendingBuff.ID) then return end
    if unit.hp > 99 then return end

    return spell:Cast(unit)
end)

-- PrayerOfHealing
PrayerofHealing:Callback('HealingPve', function(spell)
    if not isStaying then return end

    if getBelowHP(90) < 4 then return end

    return spell:Cast(player)
end)

-- Halo
Halo:Callback('HealingPve', function(spell)
    if not CanUseAoeHealing() then return end

    return spell:Cast(player)
end)

-- CircleofHealing
CircleofHealing:Callback('HealingPve', function(spell)
    if not CanUseAoeHealing() then return end

    return spell:Cast(unit)
end)

-- FlashHeal + Lightweaver
FlashHeal:Callback('Lightweaver', function(spell)
    if gameState.imCasting and gameState.imCasting == spell.spellId then return end
    if not A.LightweaverTalent:IsTalentLearned() then return end

    if player:Buff(A.LightweaverBuff.ID) then return Heal('Lightweaver') end

    local sliderValue = GetToggle(2, "LightweaverSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.FlashHeal, sliderValue) then
        return
    end

    return spell:Cast(unit)
end)

-- Heal + Lightweaver
Heal:Callback('Lightweaver', function(spell)
    if gameState.imCasting and gameState.imCasting == spell.spellId then return end
    if not A.LightweaverTalent:IsTalentLearned() then return end

    if not player:Buff(A.LightweaverBuff.ID) then return FlashHeal('Lightweaver') end

    local sliderValue = GetToggle(2, "LightweaverSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.Heal, sliderValue) then
        return
    end

    return spell:Cast(unit)
end)

-- FlashHeal
FlashHeal:Callback('HealingPve', function(spell)
    if A.LightweaverTalent:IsTalentLearned() then return end

    local FlashHealslider = GetToggle(2, "FlashHealSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.FlashHeal, FlashHealslider) then
        return
    end

    return spell:Cast(unit)
end)

-- Heal
Heal:Callback('HealingPve', function(spell)
    if A.LightweaverTalent:IsTalentLearned() then return end

    local Healslider = GetToggle(2, "HealSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.Heal, Healslider) then
        return
    end

    return spell:Cast(unit)
end)

-- PrayerOfMending
PrayerOfMending:Callback('LowPrioAndMoving', function(spell)
    local menuOption = GetToggle(2, "PrayerofMendingMenu")

    if menuOption == "2" then
        if unit:Buff(A.PrayerOfMendingBuff.ID) then return end
    elseif menuOption == "3" then
        if isStaying or unit:Buff(A.PrayerOfMendingBuff.ID) then return end
    else
        return
    end

    if not AutoHeal(unit:CallerId(), A.PrayerOfMending) then return end

    return spell:Cast(unit)
end)

-- Renew
Renew:Callback('AlwaysAndMoving', function(spell)
    local menuOption = GetToggle(2, "RenewMenu")

    if player:TalentKnown(EmpoweredRenew.id) then
        if HolyWordSerenity:TimeToFullCharges() < 5000 then return end
    end

    if menuOption == "1" then
        if unit:Buff(A.Renew.ID) then return end
    elseif menuOption == "2" then
        if isStaying or unit:Buff(A.Renew.ID) then return end
    else
        return
    end

    if unit.hp < 90 then
        return spell:Cast(unit)
    end

    local Renewslider = GetToggle(2, "RenewSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.Renew, Renewslider) then return end

    return spell:Cast(unit)

end)

-- PowerWordShield
PowerWordShield:Callback('HealingAndMoving', function(spell)
    local menuOption = GetToggle(2, "PowerWordShieldMenu")

    if menuOption == "1" then
        if unit:Buff(A.PowerWordShield.ID) then return end
    elseif menuOption == "2" then
        if isStaying or unit:Buff(A.PowerWordShield.ID) then return end
    else
        return
    end

    local PowerWordShieldslider = GetToggle(2, "PowerWordShieldSlider")
    if not AutoHealOrSlider(unit:CallerId(), A.PowerWordShield, PowerWordShieldslider) then return end

    return spell:Cast(unit)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### NORMAL DAMAGE ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- ShadowWordDeath
ShadowWordDeath:Callback('DamagePve', function(spell)
    if target.hp > 35 then return end

    return spell:Cast(target)
end)

-- Shadowfiend
Shadowfiend:Callback('DamagePve', function(spell)
    if combatTime == 0 then return end
    if player.manaPct >= 80 then return end

    return spell:Cast(target)
end)

-- HolyWordChastise
HolyWordChastise:Callback('DamagePve', function(spell)
    return spell:Cast(target)
end)

-- HolyFire
HolyFire:Callback('Buffed', function(spell)
    if not player:Buff(buffs.empyrealblaze) then return end

    return spell:Cast(target)
end)

-- HolyNova
HolyNova:Callback('DamagePve', function(spell)
    if player:HasBuffCount(390636) < 15 then return end
    if MultiUnits:GetByRange(8) < GetToggle(2, "HolyNovaSlider") then return end

    return spell:Cast(target)
end)

-- DivineStar
DivineStar:Callback('DamagePve', function(spell)
    if combatTime == 0 then return end
    if Unit("target"):GetRange() > 30 then return end

    return spell:Cast(target)
end)

-- HolyFire
HolyFire:Callback('DamagePve', function(spell)
    --if target:DebuffRemains(14914) >= 3000 then return end

    return spell:Cast(target)
end)

-- ShadowWordPain
ShadowWordPain:Callback('DamagePve', function(spell)
    if target:DebuffRemains(589) >= 3000 then return end

    return spell:Cast(target)
end)

-- Smite
Smite:Callback('DamagePve', function(spell)
    return spell:Cast(target)
end)

--################################################################################################################################################################################################################

local function Untilities()

end

local function PreCombat()
	PowerWordFortitude('PreCombat')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function SelfDefensive()
    DesperatePrayer('Defensive')
    Fade('Defensive')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function HealerRotationPve()
    if target.exists and target.isFriendly then
        unit = target
    elseif focus.exists and focus.isFriendly then
        unit = focus
    else
        return
    end

    Purify('Dispel')

    VoidShift('Emergency')
    GuardianSpirit('Emergency')

    BloodFury('Racials')
    Berserking('Racials')
    Fireblood('Racials')
    AncestralCall('Racials')
    BagOfTricks('Racials')

    HolyWordSalvation('Cooldowns')
    DivineHymn('Cooldowns')
    Apotheosis('Cooldowns')
    PowerInfusion('Cooldowns')

    PowerWordLife('HealingPve')
    PowerWordLifeToo('HealingPve')

    DivineWord('HealingPve')
    HolyWordSanctify('HealingPve')
    HolyWordSerenity('HealingPve')

    FlashHeal('Freecast')

    PrayerOfMending('HighPrio')
    Halo('HealingPve')
    CircleofHealing('HealingPve')



    FlashHeal('Lightweaver')
    Heal('Lightweaver')

    PrayerofHealing('HealingPve')

    FlashHeal('HealingPve')
    Heal('HealingPve')

    PrayerOfMending('LowPrioAndMoving')
    Renew('AlwaysAndMoving')
    PowerWordShield('HealingAndMoving')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function DamageRotationPve()
    if Player:ManaPercentage() <= GetToggle(2, "ManaTresholdDpsSlider") then return end

    ShadowWordDeath('DamagePve')
	Shadowfiend('DamagePve')
    HolyWordChastise('DamagePve')
	HolyFire('Buffed')
	HolyNova('DamagePve')
    DivineStar('DamagePve')
    HolyFire('DamagePve')
    ShadowWordPain('DamagePve')
    Smite('DamagePve')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### ARENA ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- Fade
Fade:Callback("arena", function(spell)
    local castOrChannelAone = arena1.castOrChannelInfo
    local castOrChannelAtwo = arena2.castOrChannelInfo
    local castOrChannelAthree = arena3.castOrChannelInfo
    if Action.Zone ~= "arena" then return end
    if not IsPlayerSpell(PhaseShift.id) then return end
    if arena1.exists and arena1:CastingFromFor(MakLists.arenaFadeList, 420) then
        if castOrChannelAone.percent >= 60 then
            if A.GetToggle(2, "makArenaAware") then Aware:displayMessage("Casting fade to counter incoming CC from Arena1", "White", 1) end
            return spell:Cast(player)
        end
    end

    if arena2.exists and arena2:CastingFromFor(MakLists.arenaFadeList, 420) then
        if castOrChannelAtwo.percent >= 60 then
            if A.GetToggle(2, "makArenaAware") then Aware:displayMessage("Casting fade to counter incoming CC from Arena2", "White", 1) end
            return spell:Cast(player)
        end
    end

    if arena3.exists and arena3:CastingFromFor(MakLists.arenaFadeList, 420) then
        if castOrChannelAthree.percent >= 60 then
            if A.GetToggle(2, "makArenaAware") then Aware:displayMessage("Casting fade to counter incoming CC from Arena3", "White", 1) end
            return spell:Cast(player)
        end
    end
end)

PsychicScream:Callback("arena", function(spell)
    if getBelowHP(40) > 0 then return end
    if player:Debuff(410201) then return end -- searing glare
    if player.combatTime == 0 then return end
    if not enemyHealer.exists then return end
    if enemyHealer.distance <= 3 and not enemyHealer.totalImmune and not enemyHealer.magicImmune and not enemyHealer.ccImmune and enemyHealer.ccRemains < 1000 and enemyHealer.disorientDr >= .5 and not enemyHealer:Debuff(203337) then
        if A.GetToggle(2, "makArenaAware") then Aware:displayMessage("Casting Psychic Scream on enemy healer", "White", 1) end
        return spell:Cast(player)
    end

    --[[if arena1.exists and arena1:IsHealer() and arena1.distance <= 5 and not arena1.totalImmune and not arena1.magicImmune and not arena1.ccImmune and arena1.ccRemains < 1 and arena1.disorientDr >= .5 then
        Aware:displayMessage("Casting Psychic Scream on enemy healer (arena2)", "White", 1)
        return spell:Cast(player)
    end

    if arena2.exists and arena2:IsHealer() and arena2.distance <= 5 and not arena2.totalImmune and not arena2.magicImmune and not arena2.ccImmune and arena2.ccRemains < 1 and arena2.disorientDr >= .5 then
        Aware:displayMessage("Casting Psychic Scream on enemy healer (arena2)", "White", 1)
        return spell:Cast(player)
    end

    if arena3.exists and arena3:IsHealer() and arena3.distance <= 5 and not arena3.totalImmune and not arena3.magicImmune and not arena3.ccImmune and arena3.ccRemains < 1 and arena3.disorientDr >= .5 then
        Aware:displayMessage("Casting Psychic Scream on enemy healer (arena3)", "White", 1)
        return spell:Cast(player)
    end]]
end)

-- Gate Open Pre Buffs
PremonitionofInsightArena:Callback("arenaGates", function(spell)
    if player.combatTime > 0 then return end
    if player:Buff(32727) then return end
    if party1.exists and party1:Buff(buffs.prayerOfMending) or party2.exists and party2:Buff(buffs.prayerOfMending) then return end

    return spell:Cast(player)
end)

PrayerOfMending:Callback("arenaGates", function(spell)
    if player.combatTime > 0 then return end
    if player:Buff(32727) then return end

    if party1.exists and party1:HasBuffCount(buffs.prayerOfMending) <= 8 then
        HealingEngine.SetTarget(party1:CallerId(), 1)
        return spell:Cast(party1)
    end

    if party2.exists and not party2:HasBuffCount(buffs.prayerOfMending) <= 8 then
        HealingEngine.SetTarget(party2:CallerId(), 1)
        return spell:Cast(party2)
    elseif not player:Buff(buffs.prayerOfMending) then
        HealingEngine.SetTarget(player:CallerId(), 1)
        return spell:Cast(player)
    end
end)

Renew:Callback("arenaGates", function(spell)
    if player.combatTime > 0 then return end
    if player:Buff(32727) then return end
    if Renew:Cooldown() > 0 then return end

    if party1.exists and not party1:Buff(buffs.renew) then
        HealingEngine.SetTarget(party1:CallerId(), 1)
        return spell:Cast(party1)
    end

    if party2.exists and not party2:Buff(buffs.renew) then
        HealingEngine.SetTarget(party2:CallerId(), 1)
        return spell:Cast(party2)
    elseif not player:Buff(buffs.renew) then
        HealingEngine.SetTarget(player:CallerId(), 1)
        return spell:Cast(player)
    end
end)

-- Premonitions
PremonitionofInsightArena:Callback("arena", function(spell)
    if unit.hp > 95 then return end
    --if player.combatTime == 0 then return end

    return spell:Cast(player)
end)

PremonitionofPietyArena:Callback("arena", function(spell)
    if unit.hp > 70 then return end

    return spell:Cast(player)
end)

PremonitionofSolaceArena:Callback("arena", function(spell)
    if unit.hp > 70 then return end

    return spell:Cast(player)
end)

PremonitionofClairvoyanceArena:Callback("arena", function(spell)
    if unit.hp > 70 then return end

    return spell:Cast(player)
end)

-- GuardianSpirit
GuardianSpirit:Callback('arena', function(spell)
    if VoidShift:Used() > 0 and VoidShift:Used() < 3000 then return end
    if unit.hp > 30 then return end
    if unit:Buff(232707) then return end

    return spell:Cast(unit)
end)

-- RayofHope
RayofHope:Callback('arena', function(spell)
    if VoidShift:Used() > 0 and VoidShift:Used() < 3000 then return end
    if unit.hp > 40 then return end
    if unit:Buff(47788) then return end

    return spell:Cast(unit)
end)

-- PowerWordLife
PowerWordLife:Callback('arena', function(spell)
    if player:TalentKnown(MiraculousRecovery.id) then return end
    if unit.hp > 35 then return end

    return spell:Cast(unit)
end)

PowerWordLifeToo:Callback('arena', function(spell)
    if not player:TalentKnown(MiraculousRecovery.id) then return end
    if unit.hp > 50 then return end

    return spell:Cast(unit)
end)

-- Apotheosis
Apotheosis:Callback('arena', function(spell)
    if unit.hp > 70 then return end
    if unit:Buff(47788) then return end
    if unit.hp < 50 and HolyWordSerenity:Charges() == 0 and HolyWordSanctify:Charges() == 0 then
        return spell:Cast(player)
    end

    if unit.hp < 40 and HolyWordSerenity:Charges() == 0 then
        return spell:Cast(player)
    end

    if HolyWordSerenity:Charges() <= 1 and HolyWordSanctify:Charges() <= 1 and HolyWordChastise:Cooldown() > 1000 then
        return spell:Cast(player)
    end
end)

-- HolyWordSerenity
HolyWordSerenity:Callback('arena', function(spell)
    if unit.hp > 55 then return end

    return spell:Cast(unit)
end)

-- FlashHeal
FlashHeal:Callback('arena', function(spell)
    if not player:Buff(114255) then return end
    if unit.hp > 75 then return end

    return spell:Cast(unit)
end)

-- PrayerOfMending
PrayerOfMending:Callback('arena', function(spell)
    if unit.hp > 95 then return end
    if unit:Buff(41635) and unit:BuffRemains(41635) > 2000 then return end

    return spell:Cast(unit)
end)

-- Renew
Renew:Callback('arena', function(spell)
    if unit.hp > 95 then return end
    if unit:Buff(139) and unit:BuffRemains(139) > 2000 then return end

    return spell:Cast(unit)
end)

-- HolyWordSanctify
HolyWordSanctify:Callback('arena', function(spell)
    if unit.hp > 70 then return end

    return spell:Cast(player)
end)

-- FlashHeal
FlashHeal:Callback('arena2', function(spell)
    --if not player:Buff(372313) then return end
    if unit.hp >= 75 then return end

    return spell:Cast(unit)
end)

-- FlashHeal
FlashHeal:Callback('arena3', function(spell)
    if not player:Buff(215769) then return end
    if unit.hp > 99 then return end

    return spell:Cast(unit)
end)


-- PowerWordShield
PowerWordShield:Callback('arena', function(spell)
    if unit.hp > 40 then return end

    return spell:Cast(unit)
end)

-- Shadowfiend
Shadowfiend:Callback('arena', function(spell)
    if player.manaPct >= 80 then return end
    if player:Debuff(410201) then return end
    if not CanAttackTarget() then return end

    return spell:Cast(unit)
end)

-- Holy Fire
HolyFire:Callback('arena', function(spell)
    if player:Debuff(410201) then return end
    if not CanAttackTarget() then return end
    if getBelowHP(75) > 0 then return end

    return spell:Cast(target)
end)

-- Smite
Smite:Callback('arena', function(spell)
    if player:Debuff(410201) then return end
    if not CanAttackTarget() then return end
    if getBelowHP(75) > 0 then return end

    return spell:Cast(target)
end)

local function HealerRotationPvp()
    local holdGCDforSWDE = holdGCDforSWD()
    if target.exists and target.isFriendly then
        unit = target
    elseif focus.exists and focus.isFriendly then
        unit = focus
    else
        return
    end
    Fade("arena")
    if holdGCDforSWDE then return end
    PremonitionofInsightArena('arenaGates')
    PrayerOfMending('arenaGates')
    Renew('arenaGates')
    PsychicScream("arena")
    PremonitionofInsightArena('arena')
    PremonitionofPietyArena('arena')
    PremonitionofSolaceArena('arena')
    PremonitionofClairvoyanceArena('arena')
    PowerWordLife('arena')
    PowerWordLifeToo('arena')
    RayofHope('arena')
    GuardianSpirit('arena')
    Apotheosis('arena')
    HolyWordSerenity('arena')
    FlashHeal('arena')
    PrayerOfMending('arena')
    Renew('arena')
    HolyWordSanctify('arena')
    FlashHeal('arena2')
    PowerWordShield('arena')
    Shadowfiend('arena')
    HolyFire('arena')
    Smite('arena')
end


--################################################################################################################################################################################################################

A[1] = function(icon)
    --AntiFakeCC - Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
	if A.AntiFakeCC:GetCooldown() == 0 then return A.AntiFakeCC:Show(icon) end
end

A[2] = function(icon)
	local castLeft, _, _, _, notKickAble = Unit("target"):IsCastingRemains()
	if castLeft == 0 then return end

    --AntiFakeKick --Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
    if A.AntiFakeKick:GetCooldown() == 0 and not notKickAble then return A.AntiFakeKick:Show(icon) end
end

--################################################################################################################################################################################################################

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()
    SetUpHealers()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Holy Word: Serenity CD ", HolyWordSerenity:TimeToFullCharges())

    end


	local CantCast = CantCast()
	if CantCast then return end

	isStaying   	= not player.moving
    stayingTime		= Player.stayTime
	movingTime  	= Player.moveTime
	isMoving 		= player.moving
	combatTime  	= player.combatTime
	playerHealth	= player.hp
	inMeleeRange	= target:Distance() <= 5

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    --[[
    local enemiesList = MakEnemies.get()
    local shouldInterrupt = handleInterrupts(enemiesList)
    if shouldInterrupt then
        if shouldInterrupt == "Switch" then
            return A.TargetEnemy:Show(icon)
        else
            return shouldInterrupt:Show(icon)
        end
    end
    ]]

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

	--Utilities
    Untilities()

     --PreCombat/Defensives
    if combatTime == 0 then
        PreCombat()
    else
        SelfDefensive()
    end

    --Healing Rotation PVE
    if (target.exists or focus.exists) then
        if Action.Zone ~= "arena" then
            HealerRotationPve()
        else
            HealerRotationPvp()
        end
    end

    AngelicFeather('Movement')

    --Damage Rotation PVE
    if target.exists and target.canAttack and Action.Zone ~= "arena" then
        DamageRotationPve()
    end

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### ARENA ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---
ShadowWordDeath:Callback("arenap", function(spell, enemy)
    local castOrChannel = enemy.castOrChannelInfo
    if (IsPlayerSpell(PhaseShift.id) and Fade:Cooldown() == 0) then return end
    if player:Buff(408558) then return end
    if enemy.exists and enemy:CastingFromFor(MakLists.swdList, 420) then
        if castOrChannel.percent >= 50 then
            if A.GetToggle(2, "makArenaAware") then Aware:displayMessage("Casting SW:D to counter incoming CC", "Blue", 1) end
            return spell:Cast(player)
        end
    end
end)

ShadowWordDeath:Callback("arena2", function(spell, enemy)
    if not CanAttackTarget() then return end
    if enemy.hp > 20 then return end

    return spell:Cast(enemy)
end)

DispelMagic:Callback("arenap", function(spell, enemy)
    if getBelowHP(70) > 0 then return end
    --if enemy.hp > 50 then return end
    --if PowerWordShield:Cooldown() < 2000 then return end
    if enemy:HasBuffFromFor(MakLists.purgeableBuffs, 500) then
        return spell:Cast(enemy)
    end
end)

HolyWordChastise:Callback("arenap", function(spell, enemy)
    if enemy.totalImmune then return end
    if enemy.magicImmune then return end
    if arena1.hp > 70 and arena2.hp > 70 and arena3.hp > 70 then return end
    if player:Buff(408558) then return end
    if player:Buff(410201) then return end
    if enemy.stunDr < 1 then return end

    if enemy:IsUnit(enemyHealer) and enemy.distance <= 25 then
        return spell:Cast(enemy)
    end

    if enemy:HasBuffFromFor(MakLists.DPSCooldownList, 500) then
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local enemyRotation = function(enemy)
    local holdGCDforSWDPE = holdGCDforSWDP()
    if player:Debuff(410201) then return end
    if player.channeling then return end
	if not enemy.exists then return end
    ShadowWordDeath("arenap", enemy)
    ShadowWordDeath("arena2", enemy)
    HolyWordChastise("arenap", enemy)
    if not holdGCDforSWDPE then
        DispelMagic("arenap", enemy)
    end
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### PARTY ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

VoidShift:Callback("arenap", function(spell, friendly)
    if friendly.isMe then return end
    if friendly:Buff(47788) then return end
    if friendly:Buff(232707) then return end
    if friendly.hp < 35 and player.hp > 60 and not friendly.totalImmune then
        return spell:Cast(friendly)
    end

    if player.hp < 35 and friendly.hp > 60 then
        return spell:Cast(friendly)
    end

end)

Purify:Callback("arenap", function(spell, friendly)
    if getBelowHP(50) > 0 then return end
    if friendly:Debuff(30108) or friendly:Debuff(316099) then return end -- UA
    if friendly:HasDeBuffFromFor(MakLists.arenaDispelDebuffs, 500) then
        return spell:Cast(friendly)
    end
end)

PowerInfusionP:Callback("arenap", function(spell, friendly)
    if player.combatTime == 0 then return end
    if not target.canAttack then return end
    if target.distance > 40 then return end
    if friendly:HasBuffFromFor(MakLists.DPSCooldownList, 777) then
        return spell:Cast(friendly)
    end
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local partyRotation = function(friendly)
    local holdGCDforSWDP = holdGCDforSWD()
    if player.channeling then return end
    if not friendly.exists then return end
    if holdGCDforSWDP then return end
    PowerInfusionP("arenap", friendly)
    VoidShift("arenap", friendly)
    Purify("arenap", friendly)
end

--################################################################################################################################################################################################################

A[6] = function(icon)
    if A.LightweaverTalent:IsTalentLearned() then
        if not player:Buff(A.LightweaverBuff.ID) and Unit("player"):IsCastingRemains(A.Heal.ID) > 0 then
            return A.StopCast:Show(icon)
        end
        if player:Buff(A.LightweaverBuff.ID) and Unit("player"):IsCastingRemains(A.FlashHeal.ID) > 0 then
            return A.StopCast:Show(icon)
        end
    end

    if Action.Zone == "arena" then
        if Unit("player"):IsCastingRemains(A.Smite.ID) > 0 or Unit("player"):IsCastingRemains(A.HolyFire.ID) > 0 and getBelowHP(75) > 0 then
            return A.StopCast:Show(icon)
        end
    end

    --Multi Dot
    if GetToggle(2, "SpreadDot") and not Unit("target"):IsBoss() and Unit("target"):HasDeBuffs(A.ShadowWordPain.ID, true) > 0 and (Player:GetDeBuffsUnitCount(A.ShadowWordPain.ID, true) < MultiUnits:GetActiveEnemies()) then
        return A.TargetEnemy:Show(icon)
    end

	RegisterIcon(icon)
    partyRotation(party1)
	enemyRotation(arena1)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[7] = function(icon)
	RegisterIcon(icon)
    partyRotation(party2)
	enemyRotation(arena2)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[8] = function(icon)
	RegisterIcon(icon)
    partyRotation(party3)
	enemyRotation(arena3)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################
-- NOTES
--################################################################################################################################################################################################################
-- [1] is AntiFake CC rotation (limited, usually is single color like 0x00FF00 which is green)
-- [2] is AntiFake Kick rotation (racial, primary specialization interrupt spell)
-- [3] is Rotation (old launcher called it Single, supports all actions)
-- [4] is Secondary (old launcher called it AoE) rotation (supports all actions)
-- [5] is Trinket rotation (racial, specialization's spells which can remove CC)
-- [6] is Passive rotation (limited actions, usually @raid1, @party1, @arena1 and additional binds - for more info look notes in the launcher)
-- [7] is Passive rotation (limited actions, usually @raid2, @party2, @arena2)
-- [8] is Passive rotation (limited actions, usually @raid3, @party3, @arena3)
--Passive rotation doesn't require START button use like it does [1] -> [5] rotations
