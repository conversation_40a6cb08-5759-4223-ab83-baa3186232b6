if not MakuluValidCheck() then return true end
if not Maku<PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 73 then return end

-- ========================================
-- PROTECTION WARRIOR - THE WAR WITHIN SEASON 3
-- Updated for Patch 11.2 and Season 3 content
--
-- Major Changes in Season 3 / Patch 11.2:
-- - Spell Block ability removed (replaced by passive Spellbreaker talent)
-- - New talents: Heavy Handed, Red Right Hand, Spellbreaker, Hunker Down, Armor Specialization
-- - Enhanced Execute mechanics with Heavy Handed (hits 2 additional targets)
-- - Improved Spell Reflection with Hunker Down talent
--
-- Season 3 Content:
-- - New Raid: Manaforge Omega (8 bosses)
-- - New Dungeon: Eco-Dome Al'dani
-- - Returning Dungeons: Halls of Atonement, Tazavesh (Streets of Wonder & So'leah's Gambit)
-- ========================================

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Trinket          = MakuluFramework.Trinket
local Aware            = MakuluFramework.Aware
local Debounce         = MakuluFramework.debounceSpell
local ConstCell        = cacheContext:getConstCacheCell()

local Action           = _G.Action
local ActionUnit       = Action.Unit
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle

local BossMods         = Action.BossMods

local _G, setmetatable = _G, setmetatable

local ActionID = {
    Avatar = { ID = 107574, MAKULU_INFO = { targeted = false } },
    BattleShout = { ID = 6673, MAKULU_INFO = { targeted = false } },
    BattleStance = { ID = 386164, MAKULU_INFO = { targeted = false } },
    BerserkerRage = { ID = 18499, MAKULU_INFO = { targeted = false } },
    Charge = { ID = 100, MAKULU_INFO = { damageType = "physical" } },
    DefensiveStance = { ID = 386208, MAKULU_INFO = { targeted = false } },
    Execute = { ID = 163201, MAKULU_INFO = { damageType = "physical" } },
    Hamstring = { ID = 1715, MAKULU_INFO = { damageType = "physical" } },
    HeroicLeap = { ID = 6544, MAKULU_INFO = { targeted = false } },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    ImpendingVictory = { ID = 202168, MAKULU_INFO = { targeted = false } },
    Intervene = { ID = 3411, MAKULU_INFO = { heal = true }, Macro = "/cast [@target,help][@focus,help][]spell:thisID" },
    IntimidatingShout = { ID = 5246, MAKULU_INFO = { cc = true } },
    Pummel = { ID = 6552, MAKULU_INFO = { offGcd = true } },
    RallyingCry = { ID = 97462, MAKULU_INFO = { targeted = false } },
    ShieldBlock = { ID = 2565 },
    ShieldSlam = { ID = 23922, MAKULU_INFO = { damageType = "physical" } },
    Shockwave = { ID = 46968 },
    Slam = { ID = 1464, MAKULU_INFO = { damageType = "physical" } },
    SpellReflection = { ID = 23920, MAKULU_INFO = { offGcd = true } },
    StormBolt = { ID = 107570, MAKULU_INFO = { damageType = "physical" } },
    Taunt = { ID = 355 },
    ThunderClap = { ID = 6343, MAKULU_INFO = { damageType = "physical" }, Macro = "/cast spell:thisID" },
    ThunderousRoar = { ID = 384318, MAKULU_INFO = { damageType = "physical" } },
    WhirlWind = { ID = 1680, MAKULU_INFO = { damageType = "physical" } },
    ChampionsSpear = { ID = 376079, Macro = "/cast [@player]spell:thisID" },
 
    ChallengingShout = { ID = 1161 },
    DemoralizingShout = { ID = 1160, MAKULU_INFO = { targeted = false } },
    IgnorePain = { ID = 190456, MAKULU_INFO = { targeted = false, offGcd = true } },
    LastStand = { ID = 12975 },
    Ravager = { ID = 228920, MAKULU_INFO = { damageType = "physical" }, Macro = "/cast [@player]spell:thisID" },
    Rend = { ID = 394062, MAKULU_INFO = { damageType = "physical" } },
    Revenge = { ID = 6572 },
    ShieldCharge = { ID = 385952, MAKULU_INFO = { damageType = "physical", cc = true } },
    ShieldWall = { ID = 871 },
    -- SpellBlock = { ID = 392966, MAKULU_INFO = { offGcd = true } },  -- Removed in Patch 11.2
    Demolish = { ID = 436358 }, -- Texture = 5927618 },
    ThunderBlast = { ID = 404 },
    
    ImmovableObject = { ID = 394307 },
    ChampionsBulwark = { ID = 386328 },
    BoomingVoice = { ID = 202743 },
    ImpenetrableWall = { ID = 384072 },
    HeavyRepercussions = { ID = 203177 },
    UnnervingFocus = { ID = 384042 },
    Bolster = { ID = 280001 },
    SeismicReverberation = { ID = 382956 },
    BarbaricTraining = { ID = 390675 },
    BitterImmunity = { ID =  383762 },

    -- Season 3 / Patch 11.2 New Talents
    HeavyHanded = { ID = 1235088 },
    RedRightHand = { ID = 1235038 },
    Spellbreaker = { ID = 1235023 },
    HunkerDown = { ID = 1235022 },
    ArmorSpecialization = { ID = 1234769 },

    Disarm = { ID = 236077, MAKULU_INFO = { damageType = "physical" } },

    AntiFakeKick = { Type = "SpellSingleColor", ID = 6552,  Hidden = true,		Color = "GREEN"	    , Desc = "[2] AntiFakeKick",    QueueForbidden = true	},
	AntiFakeCC	 = { Type = "SpellSingleColor", ID = 107570,  	Hidden = true,		Color = "YELLOW"	, Desc = "[1] AntiFakeCC",      QueueForbidden = true	},
    
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[ACTION_CONST_WARRIOR_PROTECTION] = A

TableToLocal(M, getfenv(1))
Aware:enable()


local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer

local gameState = {}

local buffs = {
    arena_preparation = 32727,
    power_infusion = 10060,

    battle_stance = 386164,
    thunder_blast = 435607,
    avatar = 107574,
    last_stand = 12975,
    violent_outburst = 386477,
    colossal_might = 440989,
    shield_block = 132404,
    burst_of_power = 437118,
    defensive_stance = 386208,
    rallying_cry = 97462,
    battle_shout = 6673,
    ignore_pain = 190456,
}

local debuffs = {
    exhaustion = 57723,
    rend = 772,
}

local scd = 12
if A.GetToggle(2, "SheildChargeMeleeOnly") then
    scd = 2
end

local interrupts = {
    { spell = Pummel },
    { spell = StormBolt, isCC = true },
    { spell = Shockwave, isCC = true, aoe = true, distance = 3 },
    { spell = IntimidatingShout, aoe = true, distance = 3 },
    { spell = ShieldCharge, isCC = true, distance = scd },
}

local function num(val)
    if val then return 1 else return 0 end
end

local function shouldBurst()
    if Action.BurstIsON("target") then
        return true
    end
    return false
end

local function EnemiesInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("enemiesIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy.inCombat and not enemy.isPet and not enemy.isFriendly then
                total = total + 1
            end
        end
        return total
    end)
end

local function EnemiesInSpellRangeWithoutDebuff(makulu_spell, debuff_id)
    return ConstCell:GetOrSet("enemiesInDebuff" .. makulu_spell.id .. debuff_id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy.inCombat and not enemy.isPet and not enemy.isFriendly and not enemy:Debuff(debuff_id) then
                total = total + 1
            end
        end
        return total
    end)
end

local function TotemsInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("totemsIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy.inCombat and enemy:IsTotem() and not enemy.isFriendly then
                return true
            end
        end 
        return false
    end)
end

local function TauntCountSpell(makulu_spell)
    return ConstCell:GetOrSet("tauntCount" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local needTaunt = 0
        for unitToCheck in pairs(activeEnemies) do
            local munit = MakUnit:new(unitToCheck)
            if munit and munit.exists and munit.inCombat and makulu_spell:InRange(munit) and not A.IsInPvP and not ActionUnit(unitToCheck):IsTotem() and not ActionUnit(unitToCheck):IsDummy() then
                local threatStatusE = UnitThreatSituation("player", unitToCheck)
                if threatStatusE == 0 or threatStatusE == 2 then
                    needTaunt = needTaunt + 1
                end
            end
        end
        return needTaunt
    end)
end

local function AutoTarget()
    if not player.inCombat then return false end

    if A.GetToggle(2, "autotarget") then
        for _, spellInfo in ipairs(interrupts) do
            if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
                return false
            end
        end
    end

    if A.GetToggle(2, "autotarget") and TotemsInSpellRange(Execute) and not target:IsTotem() then
        return true
    end

    if A.GetToggle(2, "autotaunt") and gameState.ShouldTaunt == "Switch" then
        return true
    end

    if Execute:InRange(target) and target.exists and (target.hp < 20 or (player:TalentKnown(281001) and target.hp < 35)) then return false end

    if A.GetToggle(2, "multidot") and EnemiesInSpellRangeWithoutDebuff(Execute, debuffs.rend) < EnemiesInSpellRange(Execute) and gameState.ShouldTaunt ~= "Taunt" then
        return true
    end

    if Execute:InRange(target) and target.exists then return false end

    if A.GetToggle(2, "targetmelee") and EnemiesInSpellRange(Execute) > 0 then
        return true
    end
end

local pveSpellReflect = {
    -- SEASON 1 TWW
    -- Raid
    436996, -- Princess
    436787, -- Princess
    437839, -- Princess
    438200, -- Court
    441772, -- Court
    439865, -- Queen
    451600, -- Queen
    -- Dungeon
    432031, -- Ara-Kara - Ki'katal
    436322, -- Ara-Kara
    434786, -- Ara-Kara
    436322, -- Ara-Kara
    -- City
    434722, 440468, 439646, 446718, 439341,
    -- Dawn
    428086, 451117,
    -- Grim
    449444, 447966, 450102, 450087,
    -- NW
    320170, 322493, 320788,
    -- Mist
    323057,
    --Vault
    428161,

    --SEASON 2 TWW
    --Liberation of Undermine
    1219386, -- Scrap Rockets
    460847, -- Electric Blast
    --Cinderbrew
    436640, -- Burning Ricochet
    437733, -- Boiling Flames
    --Darkflame
    443694, -- Crude Weapons
    422700, -- Extinguishing Gust
    421638, -- Wicklighter Barrage
    469620, -- Creeping Shadow
    428563, -- Flame Bolt
    423479, -- Wicklighter Bolt
    --Mechagon Workshop
    294860, -- Blossom Blast
    291878, -- Pulse Blast
    294195, -- Arcing Zap
    293827, -- Giga-Wallop
    1215415, -- Sticky Sludge
    -- Motherlode
    260323, -- Alpha Cannon
    263628, -- Charged Shield
    280604, -- Iced Spritzer
    262270, -- Caustic Compound
    1215934, -- Rock Lance
    268846, -- Echo Blade
    -- Operation Floodgate
    473126, -- Mudslide
    469811, -- Backwash
    465871, -- Blood Blast
    465666, -- Sparkslam
    1214468, -- Trickshot
    474388, -- Flamethrower
    465595, -- Lightning Bolt
    462776, -- Surveying Beam
    -- Priory pf the Sacred Flame
    424420, -- Cinderblast
    424421, -- Fireball
    423015, -- Castigator's Shield
    423536, -- Holy Smite
    427357, -- Holy Smite
    427469, -- Fireball
    427900, -- Molten Pool
    427951, -- Seal of Flame
    -- Theater of pain
    1217138, -- Necrotic Bolt
    1216475, -- Necrotic Bolt
    319669, -- Spectral Reach
    1222949, -- Well of Darkness
    323608, -- Dark Devastation
    324589, -- Death Bolt
    341969, -- Withering Discharge
    330697, -- Decaying Strike
    330784, -- Necrotic Bolt
    333299, -- Curse of Desolation
    330875, -- Spirit Frost
    330810, -- Bind Soul
    -- The Rookery
    430805, -- Arcing Void
    430186, -- Seeping Corruption
    430238, -- Void Bolt
    430109, -- Lightning Bolt
    467907, -- Festering Void

    --SEASON 3 TWW
    -- Manaforge Omega Raid
    -- TODO: Add Manaforge Omega spell IDs for spell reflect when available

    -- Eco-Dome Al'dani
    -- TODO: Add Eco-Dome Al'dani spell IDs for spell reflect when available

    -- Halls of Atonement (Returning)
    -- TODO: Add Halls of Atonement spell IDs for spell reflect when available

    -- Tazavesh: Streets of Wonder (Returning)
    -- TODO: Add Tazavesh Streets of Wonder spell IDs for spell reflect when available

    -- Tazavesh: So'leah's Gambit (Returning)
    -- TODO: Add Tazavesh So'leah's Gambit spell IDs for spell reflect when available
}

local pveShieldBlock = {
    -- SEASON 1 TWW
    -- City
    434722, 440468, 439646,
    -- Ara-Kara
    438471,
    -- Dawn
    451117,
    -- Grim
    447261, 450102, 449444,
    -- NW
    320655, 338456, 61269,
    -- Siege
    256867, 268230, 273470,
    -- Vault
    424888, 422233,

    --SEASON 2 TWW
    -- Liberation of Undermine
    459627, -- Tank Buster
    464112, -- Demolish
    466748, -- Infected Bite
    474406, -- Gear Grinder
    465171, -- Goblin Gravi-Gun
    473009, -- Explosive Shrapnel
    460472, -- The Big Hit
    466974, -- Goblin Gun
    466976, -- Gold Knuckles
    -- Cinderbrew
    432229, -- Keg Smash
    439031, -- Bottoms Uppercut
    438651, -- Snack Time
    436592, -- Cash Cannon
    435000, -- High Steaks
    463206, -- Tenderize
    434758, -- Throw Chair
    442995, -- Swarming Surprise
    434773, -- Mean Mug
    439468, -- Downward Trend
    -- Darkflame
    422245, -- Rock Buster
    425561, -- Nasty Nibble
    -- Mechagon Workshop
    285377, -- B.4.T.T.L.3. Mine
    282945, -- Buzz Saw
    -- Motherlode
    270926, -- Drill Smash
    267357, -- Fan of Knives
    262019, -- Grease Gun
    263586, -- Throw Shield
    -- Operation Floodgate
    460965, -- Barreling Charge
    459799, -- Wallop
    469479, -- Sludge Claws
    465128, -- Wind Up
    474350, -- Shreddation Sawblade
    468932, -- Wrench Wallop
    -- Priory of the Sacred Flame
    427621, -- Impale
    424621, -- Brutal Smash
    424426, -- Lunging Strike
    -- Theater of Pain
    320069, -- Mortal Strike
    323515, -- Hateful Strike
    318102, -- Finishing Blow
    324079, -- Reaping Scythe
    331319, -- Savage Flurry
    333845, -- Unbalancing Blow
    331224, -- Bonestorm
    -- The Rookery

    --SEASON 3 TWW
    -- Manaforge Omega Raid
    -- TODO: Add Manaforge Omega spell IDs for shield block when available

    -- Eco-Dome Al'dani
    -- TODO: Add Eco-Dome Al'dani spell IDs for shield block when available

    -- Halls of Atonement (Returning)
    -- TODO: Add Halls of Atonement spell IDs for shield block when available

    -- Tazavesh: Streets of Wonder (Returning)
    -- TODO: Add Tazavesh Streets of Wonder spell IDs for shield block when available

    -- Tazavesh: So'leah's Gambit (Returning)
    -- TODO: Add Tazavesh So'leah's Gambit spell IDs for shield block when available
}

local function wantSpellReflect()
    return ConstCell:GetOrSet("wantSpellReflect", function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for unitToCheck in pairs(activeEnemies) do
            local munit = MakUnit:new(unitToCheck)
            if munit and munit.exists and munit:IsCasting() then
                local currentCast = munit:CastInfo()
                if currentCast and currentCast.spellId and currentCast.percent > 10 and currentCast.percent < 100 and tContains(pveSpellReflect, currentCast.spellId) then
                    Aware:displayMessage("Need Spell Reflect", "Green", 1)
                    return true
                end
            end
        end
        return false
    end)
end

local function wantShieldBlock()
    return ConstCell:GetOrSet("wantShieldBlock", function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for unitToCheck in pairs(activeEnemies) do
            local munit = MakUnit:new(unitToCheck)
            if munit and munit.exists and munit:IsCasting() then
                local currentCast = munit:CastInfo()
                if currentCast and currentCast.spellId and currentCast.percent > 10 and currentCast.percent < 100 and tContains(pveShieldBlock, currentCast.spellId) then
                    Aware:displayMessage("Need Shield Block", "Green", 1)
                    return true
                end
            end
        end
        return false
    end)
end

-- Season 3 Encounter-Specific Helper Functions
-- TODO: Add specific functions for Season 3 encounters when spell IDs are available
-- Example: local function isManaforgeBoss() return ... end
-- Example: local function isEcoDomeEncounter() return ... end

local function myCast()
    local casting = player.castOrChannelInfo
    local currentCast = casting and casting.spellId

    return currentCast
end

local function updateGameState()
    gameState = {
        TWW1has2P = player:Has2Set(),
        TWW1has4P = player:Has4Set(),
        ShouldTaunt = MakuluFramework.TauntStatus(Taunt),
        tank_buster_in = MakuluFramework.DBM_TankBusterIn() or 1000000,
        stance = 0,
        ignore_pain_perc = GetToggle(2, "IgnorePainPerc"),
        ignore_pain_perc_rd = GetToggle(2, "IgnorePainPercRageDump"),
    }

    if not A.GetToggle(2, "usedbm") then
        gameState.tank_buster_in = 1000000
    end

    if player.hp < 50 then
        gameState.stance = 1
    elseif gameState.tank_buster_in < 4000 or player.hp < 50 then
        gameState.stance = 1
    end
end

-- actions.precombat+=/battle_stance,toggle=on
BattleStance:Callback("ooc", function(spell)
    if not A.GetToggle(2, "autostance") then return end
    if not player:Buff(buffs.battle_stance) and gameState.stance == 0 then
        return spell:Cast()
    end
end)

DefensiveStance:Callback("ooc", function(spell)
    if not A.GetToggle(2, "autostance") then return end
    if not player:Buff(buffs.defensive_stance) and gameState.stance == 1 then
        return spell:Cast()
    end
end)

-- actions+=/charge,if=time=0
Charge:Callback("opener", function(spell)
    if player.inCombat or target:Distance() < 8 or target:Distance() > 30 then return end

    return spell:Cast(target)
end)

-- actions+=/avatar,if=buff.thunder_blast.down|buff.thunder_blast.stack<=2
Avatar:Callback(function(spell)
    if not shouldBurst() then return end
    if not ShieldSlam:InRange(target) then return end
    if target.totalImmune or target.physImmune then return end
    if not player:Buff(buffs.thunder_blast) or player:HasBuffCount(buffs.thunder_blast) <= 2 and Pummel:InRange(target) then
        return spell:Cast()
    end
end)

-- actions+=/shield_wall,if=talent.immovable_object.enabled&buff.avatar.down
ShieldWall:Callback("apl", function(spell)
    if not shouldBurst() then return end
    if A.GetToggle(2, "defMode") then return end
    if player:TalentKnown(ImmovableObject.id) and not player:Buff(buffs.avatar) and ShieldSlam:InRange(target) then
        return spell:Cast()
    end
end)

-- actions+=/ignore_pain,if=target.health.pct>=20&(rage.deficit<=15&cooldown.shield_slam.ready|rage.deficit<=40&cooldown.shield_charge.ready|rage.deficit<=20&cooldown.shield_charge.ready|rage.deficit<=30&cooldown.demoralizing_shout.ready&talent.booming_voice.enabled|rage.deficit<=20&cooldown.avatar.ready|rage.deficit<=45&cooldown.demoralizing_shout.ready&talent.booming_voice.enabled&buff.last_stand.up&talent.unnerving_focus.enabled|rage.deficit<=30&cooldown.avatar.ready&buff.last_stand.up&talent.unnerving_focus.enabled|rage.deficit<=20|rage.deficit<=40&cooldown.shield_slam.ready&buff.violent_outburst.up&talent.heavy_repercussions.enabled&talent.impenetrable_wall.enabled|rage.deficit<=55&cooldown.shield_slam.ready&buff.violent_outburst.up&buff.last_stand.up&talent.unnerving_focus.enabled&talent.heavy_repercussions.enabled&talent.impenetrable_wall.enabled|rage.deficit<=17&cooldown.shield_slam.ready&talent.heavy_repercussions.enabled|rage.deficit<=18&cooldown.shield_slam.ready&talent.impenetrable_wall.enabled)|(rage>=70|buff.seeing_red.stack=7&rage>=35)&cooldown.shield_slam.remains<=1&buff.shield_block.remains>=4&set_bonus.tier31_2pc,use_off_gcd=1
-- NEW talent champions_bulwark it was removed in 11.2
IgnorePain:Callback(function(spell)
    if target:Distance() > 25 then return end
    if (target.hp >= 20) and (player.rageDeficit <= 15 and ShieldSlam.cd < 300 or player.rageDeficit <= 40 and ShieldCharge.cd < 300 or player.rageDeficit <= 20 and ShieldCharge.cd < 300 or player.rageDeficit <= 30 and DemoralizingShout.cd < 300 and player:TalentKnown(BoomingVoice.id) or player.rageDeficit <= 20 and Avatar.cd < 300 or player.rageDeficit <= 45 and DemoralizingShout.cd < 300 and player:TalentKnown(BoomingVoice.id) and player:Buff(buffs.last_stand) and player:TalentKnown(UnnervingFocus.id) or player.rageDeficit <= 30 and Avatar.cd < 300 and player:Buff(buffs.last_stand) and player:TalentKnown(UnnervingFocus.id) or player.rageDeficit <= 20 or player.rageDeficit <= 40 and ShieldSlam.cd < 300 and player:Buff(ViolentOutburst) and player:TalentKnown(HeavyRepercussions.id) and player:TalentKnown(ImpenetrableWall.id) or player.rageDeficit <= 55 and ShieldSlam.cd < 300 and player:Buff(buffs.violent_outburst) and player:Buff(buffs.last_stand) and player:TalentKnown(UnnervingFocus.id) and player:TalentKnown(HeavyRepercussions.id) and player:TalentKnown(ImpenetrableWall.id) or player.rageDeficit <= 17 and ShieldSlam.cd < 300 and player:TalentKnown(HeavyRepercussions.id) or player.rageDeficit <= 18 and ShieldSlam.cd < 300 and player:TalentKnown(ImpenetrableWall.id)) then
        return spell:Cast()
    end

    if player:Buff(buffs.ignore_pain) and ((player:HasBuffCount(buffs.ignore_pain) < 80 and player.rage >= 35) or (player:HasBuffCount(buffs.ignore_pain) < 96 and player.rage >= 70)) then
        return spell:Cast()
    end
end)

IgnorePain:Callback("defMode", function(spell)

    if player.rageDeficit <= 15 then
        return spell:Cast()
    end
    
    if player:Buff(buffs.ignore_pain) then return end

    return spell:Cast()
end)

-- actions+=/last_stand,if=(target.health.pct>=90&talent.unnerving_focus.enabled|target.health.pct<=20&talent.unnerving_focus.enabled)|talent.bolster.enabled|set_bonus.tier30_2pc|set_bonus.tier30_4pc
--LastStand:Callback("apl", function(spell)
--    if (target.hp >= 90 and player:TalentKnown(UnnervingFocus.id) or target.hp <= 20 and player:TalentKnown(UnnervingFocus.id)) or player:TalentKnown(Bolster.id) or gameState.TWW1has2P or gameState.TWW1has4P then
--        return spell:Cast()
--    end
--end)

-- actions+=/ravager
Ravager:Callback(function(spell)
    if target:Distance() < 6 and shouldBurst() and target.ttd > 3000 then
        return spell:Cast(target)
    end
end)

-- actions+=/demoralizing_shout,if=talent.booming_voice.enabled
DemoralizingShout:Callback(function(spell)
    if player:TalentKnown(BoomingVoice.id) and Execute:InRange(target) then
        return spell:Cast()
    end
end)

-- actions+=/champions_spear
ChampionsSpear:Callback(function(spell)
    if shouldBurst() and target:Distance() <= 2 and target.ttd > 4000 then
        return spell:Cast(target)
    end
end)

-- actions+=/thunder_blast,if=spell_targets.thunder_blast>=2&buff.thunder_blast.stack=2
ThunderousRoar:Callback("cond1", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if EnemiesInSpellRange(Execute) >= 2 and player:HasBuffCount(buffs.thunder_blast) == 2 then
        return spell:Cast(target)
    end
end)

-- actions+=/demolish,if=buff.colossal_might.stack>=3
Demolish:Callback(function(spell)
    if not shouldBurst() then return end
    if player:HasBuffCount(buffs.colossal_might) >= 3 and target.ttd > 5000 and not player.moving then
        return spell:Cast(target)
    end
end)

-- actions+=/thunderous_roar
ThunderousRoar:Callback("cond2", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

-- actions+=/shield_charge
ShieldCharge:Callback(function(spell)
    return spell:Cast(target)
end)

-- actions+=/shield_block,if=buff.shield_block.remains<=10
ShieldBlock:Callback("apl", function(spell)
    if player:BuffRemains(buffs.shield_block) <= 1000 then
        return spell:Cast()
    end
end)

-----------------------------------------------------------------------------
-- actions.aoe=thunder_blast,if=dot.rend.remains<=1
ThunderBlast:Callback("aoe", function(spell)
    if target:Debuff(debuffs.rend) <= 1 then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/thunder_clap,if=dot.rend.remains<=1
ThunderClap:Callback("aoe", function(spell)
    if not target:Debuff(debuffs.rend) and target:Distance() < 4 then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/thunder_blast,if=buff.violent_outburst.up&spell_targets.thunderclap>=2&buff.avatar.up&talent.unstoppable_force.enabled
ThunderBlast:Callback("aoe2", function(spell)
    if player:Buff(buffs.violent_outburst) and EnemiesInSpellRange(Execute) >= 2 and player:Buff(buffs.avatar) and player:TalentKnown(UnstoppableForce.id) then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/thunder_clap,if=buff.violent_outburst.up&spell_targets.thunderclap>=4&buff.avatar.up&talent.unstoppable_force.enabled&talent.crashing_thunder.enabled|buff.violent_outburst.up&spell_targets.thunderclap>6&buff.avatar.up&talent.unstoppable_force.enabled
ThunderClap:Callback("aoe2", function(spell)
    if player:Buff(buffs.violent_outburst) and EnemiesInSpellRange(Execute) >= 4 and player:Buff(buffs.avatar) and player:TalentKnown(UnstoppableForce.id) and player:TalentKnown(CrashingThunder.id) or player:Buff(buffs.violent_outburst) and EnemiesInSpellRange(Execute) > 6 and player:Buff(buffs.avatar) and player:TalentKnown(UnstoppableForce.id) then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/revenge,if=rage>=70&talent.seismic_reverberation.enabled&spell_targets.revenge>=3
Revenge:Callback("aoe", function(spell)
    if target:Distance() > 8 then return end
    if player.rage >= 70 and player:TalentKnown(SeismicReverberation.id) and EnemiesInSpellRange(Revenge) >= 3 then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/shield_slam,if=rage<=60|buff.violent_outburst.up&spell_targets.thunderclap<=4&talent.crashing_thunder.enabled
ShieldSlam:Callback("aoe", function(spell)
    if player.rage <= 60 or player:Buff(buffs.violent_outburst) and player:TalentKnown(CrashingThunder.id) and Rend:InRange(target) then -- Removed EnemiesInSpellRange(ThunderClap) <= 4 and 
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/thunder_blast
ThunderBlast:Callback("aoe3", function(spell)
    return spell:Cast(target)
end)

-- actions.aoe+=/execute,if=spell_targets.execute>=2&(rage>=50|buff.sudden_death.up)&talent.heavy_handed.enabled
-- NEWEnhanced for Season 3: Heavy Handed talent makes Execute hit 2 additional targets, Red Right Hand increases Execute damage
Execute:Callback("aoe", function(spell)
    if EnemiesInSpellRange(Execute) >= 2 and (player.rage >= 50 or player:Buff(buffs.sudden_death)) and player:TalentKnown(HeavyHanded.id) then
        return spell:Cast(target)
    end
end)

-- actions.aoe+=/thunder_clap
ThunderClap:Callback("aoe3", function(spell)
    if target:Distance() > 3 then return end
    return spell:Cast(target)
end)

-- actions.aoe+=/revenge,if=rage>=30|rage>=40&talent.barbaric_training.enabled
Revenge:Callback("aoe2", function(spell)
    if target:Distance() > 8 then return end
    if player.rage >= 30 or player.rage >= 40 and player:TalentKnown(BarbaricTraining.id) then
        return spell:Cast(target)
    end
end)

-- actions+=/run_action_list,name=aoe,if=spell_targets.thunder_clap>=3
local aoerot = function()
    if EnemiesInSpellRange(Execute) < 3 then return end

    ThunderBlast("aoe")
    ThunderClap("aoe")
    ThunderBlast("aoe2")
    Execute("aoe") -- NEW
    ThunderClap("aoe2")
    Revenge("aoe")
    ShieldSlam("aoe")
    ThunderBlast("aoe3")
    ThunderClap("aoe3")
    Revenge("aoe2")

end

-- actions.generic=thunder_blast,if=(buff.thunder_blast.stack=2&buff.burst_of_power.stack<=1&buff.avatar.up&talent.unstoppable_force.enabled)
ThunderBlast:Callback("generic", function(spell)
    if player:HasBuffCount(buffs.thunder_blast) == 2 and player:HasBuffCount(buffs.burst_of_power) <= 1 and player:Buff(buffs.avatar) and player:TalentKnown(UnstoppableForce.id) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/shield_slam,if=(buff.burst_of_power.stack=2&buff.thunder_blast.stack<=1|buff.violent_outburst.up)|rage<=70&talent.demolish.enabled
ShieldSlam:Callback("generic", function(spell)
    if (player:HasBuffCount(buffs.burst_of_power) == 2 and player:HasBuffCount(buffs.thunder_blast) <= 1 or player:Buff(buffs.violent_outburst)) or player.rage <= 70 and player:TalentKnown(Demolish.id) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/execute,if=rage>=70|(rage>=40&cooldown.shield_slam.remains&talent.demolish.enabled|rage>=50&cooldown.shield_slam.remains)|buff.sudden_death.up&talent.sudden_death.enabled
Execute:Callback("generic", function(spell)
    if player.rage >= 70 or (player.rage >= 40 and ShieldSlam.cd > 0 and player:TalentKnown(Demolish.id) or player.rage >= 50 and ShieldSlam.cd > 0) or player:Buff(buffs.sudden_death) and player:TalentKnown(SuddenDeath.id) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/shield_slam
ShieldSlam:Callback("generic2", function(spell)
    return spell:Cast(target)
end)

-- actions.generic+=/thunder_blast,if=dot.rend.remains<=2&buff.violent_outburst.down
ThunderBlast:Callback("generic2", function(spell)
    if target:Debuff(debuffs.rend) <= 2 and not player:Buff(buffs.violent_outburst) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/thunder_blast
ThunderBlast:Callback("generic3", function(spell)
    return spell:Cast(target)
end)

-- actions.generic+=/thunder_clap,if=dot.rend.remains<=2&buff.violent_outburst.down
ThunderClap:Callback("generic", function(spell)
    if target:Distance() > 4 then return end
    if target:HasDeBuffCount(debuffs.rend) <= 2 and not player:Buff(buffs.violent_outburst) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/thunder_blast,if=(spell_targets.thunder_clap>1|cooldown.shield_slam.remains&!buff.violent_outburst.up)
ThunderBlast:Callback("generic4", function(spell)
    if (EnemiesInSpellRange(Execute) > 1 or ShieldSlam.cd > 0 and not player:Buff(buffs.violent_outburst)) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/thunder_clap,if=(spell_targets.thunder_clap>1|cooldown.shield_slam.remains&!buff.violent_outburst.up)
ThunderClap:Callback("generic2", function(spell)
    if target:Distance() > 4 then return end
    if (EnemiesInSpellRange(Execute) > 1 or ShieldSlam.cd > 0 and not player:Buff(buffs.violent_outburst)) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/revenge,if=(rage>=80&target.health.pct>20|buff.revenge.up&target.health.pct<=20&rage<=18&cooldown.shield_slam.remains|buff.revenge.up&target.health.pct>20)|(rage>=80&target.health.pct>35|buff.revenge.up&target.health.pct<=35&rage<=18&cooldown.shield_slam.remains|buff.revenge.up&target.health.pct>35)&talent.massacre.enabled
Revenge:Callback("generic", function(spell)
    if target:Distance() > 8 then return end
    if (player.rage >= 80 and target.hp > 20 or player:Buff(buffs.revenge) and target.hp <= 20 and player.rage <= 18 and ShieldSlam.cd > 0 or player:Buff(buffs.revenge) and target.hp > 20) or (player.rage >= 80 and target.hp > 35 or player:Buff(buffs.revenge) and target.hp <= 35 and player.rage <= 18 and ShieldSlam.cd > 0 or player:Buff(buffs.revenge) and target.hp > 35) and player:TalentKnown(Massacre.id) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/execute
-- Enhanced for Season 3: Heavy Handed and Red Right Hand talents improve Execute
Execute:Callback("generic2", function(spell)
    return spell:Cast(target)
end)

-- actions.generic+=/revenge
Revenge:Callback("generic2", function(spell)
    if target:Distance() > 8 then return end
    return spell:Cast(target)
end)

-- actions.generic+=/thunder_blast,if=(spell_targets.thunder_clap>=1|cooldown.shield_slam.remains&buff.violent_outburst.up)
ThunderBlast:Callback("generic5", function(spell)
    if (EnemiesInSpellRange(Execute) >= 1 or ShieldSlam.cd > 0 and player:Buff(buffs.violent_outburst)) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/thunder_clap,if=(spell_targets.thunder_clap>=1|cooldown.shield_slam.remains&buff.violent_outburst.up)
ThunderClap:Callback("generic3", function(spell)
    if target:Distance() > 4 then return end
    if (EnemiesInSpellRange(Execute) >= 1 or ShieldSlam.cd > 0 and player:Buff(buffs.violent_outburst)) then
        return spell:Cast(target)
    end
end)

-- actions.generic+=/devastate
Slam:Callback("generic", function(spell)
    return spell:Cast(target)
end)

-- actions+=/call_action_list,name=generic
local genericrot = function()
    ThunderBlast("generic")
    ShieldSlam("generic")
    Execute("generic")
    ShieldSlam("generic2")
    ThunderBlast("generic2")
    ThunderBlast("generic3")
    ThunderClap("generic")
    ThunderBlast("generic4")
    ThunderClap("generic2")
    Revenge("generic")
    Execute("generic2")
    Revenge("generic2")
    ThunderBlast("generic5")
    ThunderClap("generic3")
    Slam("generic")
end

Taunt:Callback(function(spell)
    local noAggro = UnitThreatSituation("player", target:CallerId())
    if noAggro == 0 or noAggro == 2 then
        return spell:Cast(target)
    end
end)

ChallengingShout:Callback(function(spell)
    if (gameState.ShouldTaunt == "Taunt" or gameState.ShouldTaunt == "Switch") and TauntCountSpell(Hamstring) > 2 then
        return spell:Cast()
    end
end)

HeroicThrow:Callback(function(spell)
    if target:Distance() > 30 or target:Distance() < 8 then return end
    if Taunt.cd < 300 or gameState.ShouldTaunt ~= "Taunt" then return end

    return spell:Cast(target)
end)

SpellReflection:Callback(function(spell)
    if wantSpellReflect() then
        return spell:Cast()
    end
end)

--  ?? not sure this logic is right ??   --
-- SpellReflection:Callback("pve", function(spell)
--     if (arena1.exists and arena1:CastingFromFor(MakLists.arenaSpellReflect, 500)) or (arena2.exists and arena2:CastingFromFor(MakLists.arenaSpellReflect, 500)) or arena3.exists and arena3:CastingFromFor(MakLists.arenaSpellReflect, 500) then
--         return spell:Cast(target)
--     end
-- end)

-- NOTE: Spell Block was removed in Patch 11.2 - keeping callback commented for reference
-- The new Spellbreaker talent provides passive 4% chance to reduce magic damage by 50%
-- SpellBlock:Callback(function(spell)
--     if wantSpellBlock() then
--         return spell:Cast()
--     end
-- end)

ShieldBlock:Callback("def", function(spell)
    if player.rage >= 30 and not player:Buff(buffs.rallying_cry) and not player:Buff(buffs.last_stand) and player.hp < 70 then
        return spell:Cast()
    end

    if wantShieldBlock() then
        return spell:Cast()
    end
end)

LastStand:Callback("def", function(spell)
    if player.hp < 40 or gameState.tank_buster_in < 1500 then
        return spell:Cast()
    end
end)

BitterImmunity:Callback("def", function(spell)
    if player.hp < 40 then
        return spell:Cast()
    end
end)

RallyingCry:Callback("def", function(spell)
    local inDangerish = MakMulti.party:Count(function(unit) return unit.hp > 0 and unit.hp < 50 and Intervene:InRange(unit) end)
    local fullSquad = MakMulti.party:Count(function(unit) return unit.hp > 0 and Intervene:InRange(unit) end)
    if (fullSquad > 5 and inDangerish > fullSquad / 4) or (fullSquad < 6 and inDangerish >= 3) or (fullSquad < 3 and inDangerish > 0) then
        return spell:Cast()
    end
end)

ShieldWall:Callback("def", function(spell)
    if player.hp < 60 and not player:Buff(buffs.rallying_cry) and not player:Buff(buffs.last_stand) and not player:Buff(buffs.shield_block) then
        return spell:Cast()
    end
end)

BerserkerRage:Callback(function(spell)
    if not player:HasDeBuff(MakLists.feared) then return end

    return Debounce("berRage", 350, 2500, spell)
end)

BattleShout:Callback(function(spell)
    if player.inCombat then return end
    if player:HasBuff(buffs.battle_shout) then return end

    return spell:Cast()
end)

ImpendingVictory:Callback(function(spell)
    if not player.inCombat then return end

    if player.hp < 100 and target.ttd < 2500 and player.rage >= 10 then
        return spell:Cast(target)
    end
    
    if player.hp > 80 or target.ttd < 2500 then return end
    if player.rage < 10 then return end

    return spell:Cast(target)
end)

SpellReflection:Callback("pvp-arena", function(spell)
    if not Action.Zone == "arena" then return end
    if (arena1.exists and arena1:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel.cd > 1000)) or (arena2.exists and arena2:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena2.distance > 5 or Pummel:Cooldown() > 1000)) or (arena3.exists and arena3:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel:Cooldown() > 1000)) then
        return spell:Cast() -- No target required for Spell Reflection
    end
end)

ShieldCharge:Callback("arena", function(spell)
    if not Action.Zone == "arena" then return end
    if target:Distance() > 8 then return end
    if player.rage < 20 then return end

    return spell:Cast(target)
end)

A[1] = function(icon)
    --AntiFakeCC - Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
	if A.AntiFakeCC:GetCooldown() == 0 then return A.AntiFakeCC:Show(icon) end
end

A[2] = function(icon)
	local castLeft, _, _, _, notKickAble = ActionUnit("target"):IsCastingRemains()
	if castLeft == 0 then return end

    --AntiFakeKick --Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
    if A.AntiFakeKick:GetCooldown() == 0 and not notKickAble then return A.AntiFakeKick:Show(icon) end
end

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    makInterrupt(interrupts)

    if player.inCombat and MakuluFramework.TankDefensive() then
        if Trinket(13, "Defensive") then
            Trinket1()
        end
        if Trinket(14, "Defensive") then
            Trinket2()
        end
    end

    --[[if player.inCombat and player.hp < 50 then
        if MakuluFramework.CanUseHealthStone() then
            HealthStone()
        end

        if MakuluFramework.CanUseHealthPotion() then
            HealthPotion()
        end
    end]]

    if gameState.tank_buster_in <= 5000 then
        Aware:displayMessage("Tank Buster Soon", "Green", 1)
    end

    if player.inCombat then
        ShieldBlock("def")
        if A.GetToggle(2, "defMode") then ShieldBlock("apl") end
        if A.GetToggle(2, "defMode") then IgnorePain("defMode") end
        LastStand("def")
        ShieldWall("def")
        BerserkerRage()
        SpellReflection("pvp-arena")
        BitterImmunity("def")
        RallyingCry("def")
    end

    BattleStance("ooc")
    DefensiveStance("ooc")
    IgnorePain()

    BattleShout()

    if target.exists and target.canAttack then
        Taunt()
        HeroicThrow()
        ChallengingShout()

        SpellReflection()
        -- SpellBlock() -- Removed in Patch 11.2

        if shouldBurst() and target:Distance() < 4 then
            if Trinket(1, "Damage") then Trinket1() end
            if Trinket(2, "Damage") then Trinket2() end
        end

        ImpendingVictory()

        -- pew pew
        Charge("opener")
        Avatar()
        ShieldWall("apl")
        IgnorePain()
        --LastStand("apl")
        if ShieldSlam:InRange(target) then
            Ravager()
            DemoralizingShout()
            ChampionsSpear()
            ThunderousRoar("cond1")
            Demolish()
            ThunderousRoar("cond2")
            --ShieldCharge()
            ShieldBlock("apl")
            ShieldCharge("arena")

            aoerot()
            genericrot()
        end
    end

	return FrameworkEnd()
end

Pummel:Callback("arena", function(spell, enemy)
    if enemy:IsKickImmune() then return end
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_healer", function(spell, enemy)
    if enemy.ccImmune then return end
    if not enemy.isHealer then return end
    if enemy:IsTarget() then return end
    if target.hp > 50 then return end
    if enemy.stunDr < 0.5 then return end
    if enemy:CCRemains() > 2000 then return end
    Aware:displayMessage("SB - Enemy Healer - KT Low", "Blue", 1)
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_kill", function(spell, enemy)
    if enemy.ccImmune then return end
    if not enemy:IsTarget() then return end
    if enemy.stunDr < 0.5 then return end
    if enemyHealer.exists and enemy:IsUnit(enemyHealer) then return end
    if enemyHealer:CCRemains() < 2000 then return end
    if enemy.hp > 50 then return end
    Aware:displayMessage("SB - KT - Healer CCed", "Red", 1)
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_nohealer_kill", function(spell, enemy)
    if enemy.ccImmune then return end
    if enemyHealer.exists then return end
    if not enemy:IsTarget() then return end
    if enemy.stunDr < 0.5 then return end
    if enemy.hp > 50 then return end
    Aware:displayMessage("SB - KT - No Enemy Healer Exists", "Red", 1)
    return spell:Cast(enemy)
end)

Charge:Callback("charge_fear", function(spell, enemy)
    if enemy:IsTarget() then return end
    if enemy.distance < 8 then return end
    if enemy.hp > 50 then return end
    if not enemy.isHealer then return end
    if IntimidatingShout:Cooldown() > 0 then return end
    if enemy.totalImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    Aware:displayMessage("Charge - Enemy Healer - To Fear", "Blue", 1)
    return spell:Cast(enemy)
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    if enemy.ccImmune then return end
    if not spell:InRange(enemy) then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:IsTarget() then return end
    if target.hp > 50 then return end
    if target.totalImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    Aware:displayMessage("Fear - Enemy Healer - KT Low", "Blue", 1)
    return spell:Cast(enemy)
end)

Disarm:Callback("arena", function(spell, enemy)
    if enemy.totalImmune then return end
    if not enemy:HasBuffFromFor(MakLists.Disarm, 500) then return end
    Aware:displayMessage("Disarm - Enemy - Bursting", "White", 1)
    return spell:Cast(enemy)
end)

Intervene:Callback("party", function(spell, friendly)
    if friendly:IsUnit(player) then return end
    if friendly.hp > 40 then return end
    if player.hp < 40 then return end
    if friendly.hp > target.hp then return end
    if target.hp < 30 then return end

    return spell:Cast(friendly)
end)


local enemyRotation = function(enemy)
	if not enemy.exists then return end

    Pummel("arena", enemy)
    StormBolt("arena_healer", enemy)
    StormBolt("arena_kill", enemy)
    Charge("charge_fear", enemy)
    IntimidatingShout("arena", enemy)
    Disarm("arena", enemy)
end


local partyRotation = function(friendly)
    if not friendly.exists then return end

    Intervene("party", friendly)
end

A[6] = function(icon)
	RegisterIcon(icon)
    if targetForInterrupt(interrupts) then return TabTarget() end
    if AutoTarget() then return TabTarget() end
	enemyRotation(arena1)
	partyRotation(party1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena2)
	partyRotation(party2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena3)
	partyRotation(party3)

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end
