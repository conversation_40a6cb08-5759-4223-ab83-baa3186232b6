local _, MakuluFramework   = ...
MakuluFramework            = MakuluFramework or _G.MakuluFramework

MakuluAware                = {}
MakuluAware.__index        = MakuluAware
MakuluAware.activeMessages = {}
MakuluAware.messageFrames  = {}
MakuluAware.messageTexts   = {}

local GetTime              = GetTime

MakuluAware.colors         = {
    White = { 1, 1, 1, 1 },
    <PERSON> = { 0, 0, 1, 1 },
    <PERSON> = { 1, 0, 0, 1 },
    <PERSON> = { 0, 1, 0, 1 },
    <PERSON> = { 128 / 255, 0, 128 / 255, 1 },
    <PERSON> = { 1, 0.96, 0.41, 1 },
    <PERSON>lad<PERSON> = { 0.96, 0.55, 0.73, 1 },
    <PERSON><PERSON> = { 0.58, 0.51, 0.79, 1 },
    <PERSON> = { 1, 1, 1, 1 },
    <PERSON><PERSON> = { 0.41, 0.8, 0.94, 1 },
    <PERSON> = { 0.67, 0.83, 0.45, 1 },
    <PERSON><PERSON> = { 0, 0.44, 0.87, 1 },
    <PERSON> = { 0.78, 0.61, 0.43, 1 },
    <PERSON>Knight = { 0.77, 0.12, 0.23, 1 },
    <PERSON> = { 0, 1, 0.59, 1 },
    Druid = { 1, 0.49, 0.04, 1 },
    <PERSON>Hunter = { 0.64, 0.19, 0.79, 1 },
    Evoker = { 0.41, 0.8, 0.94, 1 },
}

function MakuluAware.new()
    local instance = setmetatable({}, MakuluAware)
    instance.timeSinceLastUpdate = 0
    instance.isEnabled = false -- Default to false
    return instance
end

-- Enable the message system
function MakuluAware:enable()
    self.isEnabled = true
end

-- Disable the message system
function MakuluAware:disable()
    self.isEnabled = false
end

function MakuluAware:BumpText(text, length)
    local frame = self.messageTexts[text]
    if not frame then return end

    local found = self.activeMessages[frame]
    if not found then return end

    local now = GetTime()
    found.updated = now

    length = length or 5

    C_Timer.After(length, function()
        if found.updated ~= now then return end
        self:removeMessage(frame, text)
    end)

    return true
end

local IconWidth = 50
local IconPadding = 10

local innerSize = 45
local innerTextureSize = 39

local animInTime = 0.5
local animTransitionTime = 0.2
local animOutTime = 0.2

local measuringFontString

function MakuluAware:displayMessage(text, color, length, icon, isPriority)
    if not self.isEnabled then return end
    if self.messageTexts[text] then
        if self:BumpText(text, length) then
            return
        end
    end

    local frame = self:getNextAvailableFrame()

    -- Measure the text width (match display flags to avoid deltas)
    measuringFontString = measuringFontString or UIParent:CreateFontString(nil, "ARTWORK")
    measuringFontString:SetFont("Fonts\\FRIZQT__.TTF", 20, "OUTLINE")
    measuringFontString:SetWordWrap(false)
    measuringFontString:SetNonSpaceWrap(false)
    measuringFontString:SetText(text)
    measuringFontString:SetAlpha(0)
    measuringFontString:Show()
    local textWidth = math.ceil(measuringFontString:GetStringWidth()) + 2 -- small safety buffer
    measuringFontString:Hide()

    frame:ClearAllPoints()
    frame:SetPoint("TOP", UIParent, "TOP", 0, -120)
    frame:SetFrameStrata("TOOLTIP") -- Keep on top

    -- Create subframes once
    if not frame._textFrame then
        frame._textFrame = CreateFrame("Frame", nil, frame)

        local fontString = frame._textFrame:CreateFontString(nil, "ARTWORK")
        frame._fontString = fontString

        fontString:SetFont("Fonts\\FRIZQT__.TTF", 20, "OUTLINE")
        fontString:SetShadowOffset(2, -2)
        fontString:SetJustifyH("RIGHT")
        fontString:SetJustifyV("MIDDLE")
        fontString:SetWordWrap(false)
        fontString:SetNonSpaceWrap(false)
        fontString:SetMaxLines(1)
    end

    -- Reserve space on the left if an icon is shown
    local leftInset = 0
    if icon then
        leftInset = IconWidth + IconPadding
    end

    -- Size the outer frame to fit: [icon column] + [text width]
    local frameWidth = leftInset + textWidth
    frame:SetSize(frameWidth, 50)

    -- Make the text frame span from leftInset to the right edge
    frame._textFrame:ClearAllPoints()
    frame._textFrame:SetPoint("TOPRIGHT", frame, "TOPRIGHT", 0, 0)
    frame._textFrame:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 0, 0)
    frame._textFrame:SetPoint("TOPLEFT", frame, "TOPLEFT", leftInset, 0)
    frame._textFrame:SetPoint("BOTTOMLEFT", frame, "BOTTOMLEFT", leftInset, 0)

    -- Anchor the fontstring to all four edges of the text frame
    local fontString = frame._fontString
    fontString:ClearAllPoints()
    fontString:SetPoint("LEFT",   frame._textFrame, "LEFT", 0, 0)
    fontString:SetPoint("RIGHT",  frame._textFrame, "RIGHT", 0, 0)
    fontString:SetPoint("TOP",    frame._textFrame, "TOP", 0, 0)
    fontString:SetPoint("BOTTOM", frame._textFrame, "BOTTOM", 0, 0)

    -- Apply text and color
    fontString:SetText(text)
    local colorValues = self.colors[color] or self.colors.White
    fontString:SetTextColor(unpack(colorValues))
    length = length or 5
    frame._textFrame:Show()

    -- Icon handling: ensure the icon column actually reserves width on the left
    if icon then
        if not frame._iconFrame then
            local iconFrame = CreateFrame("Frame", nil, frame)
            frame._iconFrame = iconFrame

            iconFrame.tex = iconFrame:CreateTexture(nil, "ARTWORK")
            iconFrame.tex:SetAllPoints(iconFrame)
            iconFrame.tex:SetTexture("Interface/Tooltips/UI-Tooltip-Background")
            iconFrame.tex:SetColorTexture(0, 0, 0, 1)

            iconFrame.mask = iconFrame:CreateMaskTexture()
            iconFrame.mask:SetAllPoints(iconFrame.tex)
            iconFrame.mask:SetTexture("Interface/CHARACTERFRAME/TempPortraitAlphaMask",
                "CLAMPTOBLACKADDITIVE", "CLAMPTOBLACKADDITIVE")
            iconFrame.tex:AddMaskTexture(iconFrame.mask)

            iconFrame._innerTexture = iconFrame:CreateTexture(nil, "ARTWORK")
            iconFrame._innerTexture:SetPoint("CENTER", iconFrame, "CENTER", 0, 0)
            iconFrame._innerTexture:SetTexCoord(0.07, 0.93, 0.07, 0.93)
            iconFrame._innerTexture:SetSize(innerTextureSize, innerTextureSize)

            local mask = iconFrame:CreateMaskTexture()
            mask:SetAllPoints(iconFrame._innerTexture)
            mask:SetSize(innerTextureSize, innerTextureSize)
            mask:SetTexture("Interface/CHARACTERFRAME/TempPortraitAlphaMask",
                "CLAMPTOBLACKADDITIVE", "CLAMPTOBLACKADDITIVE")
            iconFrame._innerTexture:AddMaskTexture(mask)
        end

        frame._iconFrame:ClearAllPoints()
        frame._iconFrame:SetPoint("LEFT", frame, "LEFT", 0, 0)
        frame._iconFrame:SetSize(IconWidth, 50) -- reserve full column width
        frame._iconFrame._innerTexture:SetTexture(icon)
        frame._iconFrame:Show()
    else
        if frame._iconFrame then frame._iconFrame:Hide() end
    end

    -- Animation plumbing (unchanged, just reset)
    frame.scaleAnim = nil
    frame.animGroup = frame.animGroup or frame:CreateAnimationGroup()
    frame:SetAlpha(0)
    frame:Show()

    -- Track message
    local now = GetTime()
    local storedMessage = {
        text = text,
        isPriority = isPriority or false,
        updated = now,
        position = -120,
        isNew = true,
        currScale = 0.5,
        destroying = false
    }

    self.activeMessages[frame] = storedMessage
    self.messageTexts[text] = frame

    self:adjustMessagePositions()

    -- Auto-remove after length unless bumped
    return C_Timer.After(length, function()
        if storedMessage.updated ~= now then return end
        self:removeMessage(frame, text)
    end)
end

function MakuluAware:adjustMessagePositions()
    local index = 0
    local activeFrames = self:getActiveFramesInOrder()

    table.sort(activeFrames, function(a, b)
        local aMessage = self.activeMessages[a]
        local bMessage = self.activeMessages[b]

        if not aMessage or not bMessage then return false end

        local isAPriority = aMessage.isPriority
        local isBPriority = bMessage.isPriority

        if isAPriority == isBPriority then
            return aMessage.updated < bMessage.updated
        else
            return isAPriority
        end
    end)

    for i = #activeFrames, 1, -1 do
        local targetY = -120 - (55 * (index))

        local frame = activeFrames[i]
        local storedMessage = self.activeMessages[frame]
        local doingTranslation = storedMessage.position ~= targetY

        local animGroup = frame.animGroup

        local currentScale = storedMessage.currScale

        if animGroup:IsPlaying() then
            if frame.scaleAnim then
                local startSize = frame.scaleAnim:GetScaleFrom()
                local endSize = frame.scaleAnim:GetScaleTo()
                currentScale = startSize + ((endSize - startSize) * frame.scaleAnim:GetSmoothProgress())
            end

            animGroup:Stop()
        end
        animGroup:RemoveAnimations()

        local fade = animGroup:CreateAnimation("Alpha")
        local targetAlpha = math.max(1 - (0.25 * (index)), 0)
        if currentScale ~= 1 then
            local toScale = 1 -- was: targetAlpha
            scale = animGroup:CreateAnimation("Scale")
            scale:SetScaleFrom(currentScale, currentScale)
            scale:SetScaleTo(toScale, toScale)
            scale:SetOrigin("CENTER", 0, 0)
            scale:SetDuration(animTransitionTime)
            scale:SetSmoothing("IN")

            frame.scaleAnim = scale
            storedMessage.isNew = false
        end
        fade:SetFromAlpha(frame:GetAlpha())
        fade:SetToAlpha(targetAlpha)
        fade:SetDuration(animTransitionTime)
        fade:SetSmoothing("IN_OUT")

        local move
        local scale
        if doingTranslation then
            local _, _, _, _, yOfs = frame:GetPoint()
            local translationY = targetY - yOfs

            move = animGroup:CreateAnimation("Translation")
            move:SetOffset(0, translationY)
            move:SetDuration(animTransitionTime)
            move:SetSmoothing("IN_OUT")
        end

        if currentScale ~= 1 then
            scale = animGroup:CreateAnimation("Scale")
            scale:SetScaleFrom(currentScale, currentScale)
            scale:SetScaleTo(targetAlpha, targetAlpha)
            scale:SetOrigin("CENTER", 0, 0)
            scale:SetDuration(animTransitionTime)
            scale:SetSmoothing("IN")

            frame.scaleAnim = scale

            storedMessage.isNew = false
        end

        animGroup:SetScript("OnFinished", function()
            frame:SetAlpha(targetAlpha)

            if frame.scaleAnim then
                if frame.scaleAnim:GetProgress() == 1 then
                    storedMessage.currScale = frame.scaleAnim:GetScaleTo()
                    frame.scaleAnim = nil
                end
            end

            frame:SetPoint("TOP", UIParent, "TOP", 0, targetY)
            storedMessage.position = targetY
        end)

        animGroup:Play()
        index = index + 1
    end
end

-- Get active frames in order
function MakuluAware:getActiveFramesInOrder()
    local activeFrames = {}
    for frame, isActive in pairs(self.activeMessages) do
        if isActive and not frame.destroying then
            table.insert(activeFrames, frame)
        end
    end
    return activeFrames
end

-- Remove a message
function MakuluAware:removeMessage(frame, text)
    local animGroup = frame.animGroup
    animGroup:Stop()
    animGroup:RemoveAnimations()

    local fade = animGroup:CreateAnimation("Alpha")
    fade:SetFromAlpha(frame:GetAlpha())
    fade:SetToAlpha(0)
    fade:SetDuration(0.2)

    animGroup:SetScript("OnFinished", function()
        frame:SetAlpha(0)
        frame:Hide()

        frame.destroying = false
        self.activeMessages[frame] = false
    end)

    self.messageTexts[text] = nil
    frame.destroying = true
    animGroup:Play()
    self:adjustMessagePositions()
end

-- Get the next available frame
function MakuluAware:getNextAvailableFrame()
    for frame, isActive in pairs(self.activeMessages) do
        if not isActive and not frame.destroying then
            return frame
        end
    end

    local newFrame = CreateFrame("Frame", nil, UIParent)
    self.activeMessages[newFrame] = false
    return newFrame
end

-- Initialize and enable Aware system
MakuluFramework.Aware = MakuluAware.new()
MakuluFramework.MakuluAware = MakuluFramework.Aware
MakuluFramework.Aware:enable()

--Makulu Print
--Usage: MakPrint(index, "String text: ", variableToTrack)
local function RGBToHex(r, g, b)
    return string.format("%02x%02x%02x", r * 255, g * 255, b * 255)
end

local function ColorizeText(text, r, g, b)
    local hex = RGBToHex(r, g, b)
    return "|cFF" .. hex .. text .. "|r"
end

local DebugFrame = CreateFrame("Frame", "DebugFrame", UIParent, "BackdropTemplate")
DebugFrame:SetSize(300, 150)
DebugFrame:SetPoint("CENTER")
DebugFrame:EnableMouse(true)
DebugFrame:SetMovable(true)
DebugFrame:SetUserPlaced(true)
DebugFrame:SetBackdrop({
    bgFile = "Interface/Tooltips/UI-Tooltip-Background",
    edgeFile = "Interface/Tooltips/UI-Tooltip-Border",
    tile = true,
    tileSize = 16,
    edgeSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})
DebugFrame:SetBackdropColor(0, 0, 0, 1)
DebugFrame:Hide()

local messages = {}
for i = 1, 12 do
    messages[i] = DebugFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    messages[i]:SetPoint("TOPLEFT", 10, -10 * i)
    messages[i]:SetWidth(280)
    messages[i]:SetHeight(20)
    messages[i]:SetJustifyH("LEFT")
end

local hideTimer = 5
local timer = 0
local triedFetch = false

function MakPrint(index, prefix, var)
    if messages[index] then
        local coloredPrefix = ColorizeText(prefix, 1, 0, 0)
        local coloredVar = ColorizeText(tostring(var), 0, 1, 0)
        messages[index]:SetText(coloredPrefix .. " " .. coloredVar)
        if not DebugFrame:IsShown() then
            DebugFrame:Show()

            if not triedFetch and not DebugFrame.savedPoint then
                triedFetch = true
                DebugFrame.savedPoint = MakuluFramework.TryGetPersistValue("DebugFramePos", true)
            end

            if DebugFrame.savedPoint then
                DebugFrame:ClearAllPoints()
                DebugFrame:SetPoint(unpack(DebugFrame.savedPoint))
            end
        end
        timer = 0
    end
end

DebugFrame:SetScript("OnUpdate", function(self, elapsed)
    if self:IsShown() then
        timer = timer + elapsed
        if timer >= hideTimer then
            timer = 0
        end
    end
end)

DebugFrame:SetScript("OnMouseDown", function(self, button)
    if button == "LeftButton" then
        self:StartMoving()
    end
end)

DebugFrame:SetScript("OnMouseUp", function(self, button)
    self:StopMovingOrSizing()
    local point, relativeTo, relativePoint, x, y = self:GetPoint()
    self.savedPoint = {point, relativeTo, relativePoint, x, y}
    MakuluFramework.SavePersistentValue("DebugFramePos", self.savedPoint, true)
end)

-- Reload on Spec Change
if WOW_PROJECT_ID ~= WOW_PROJECT_MISTS_CLASSIC then
    local specChangeFrame = CreateFrame("Frame", "specChangeFrame", UIParent, "BasicFrameTemplateWithInset")
    specChangeFrame:SetSize(400, 150)
    specChangeFrame:SetPoint("CENTER", UIParent, "TOP", 0, -200)
    specChangeFrame:Hide()

    specChangeFrame.title = specChangeFrame:CreateFontString(nil, "OVERLAY")
    specChangeFrame.title:SetFontObject("GameFontHighlight")
    specChangeFrame.title:SetPoint("LEFT", specChangeFrame.TitleBg, "LEFT", 5, 0)
    specChangeFrame.title:SetText("WeakAura")

    specChangeFrame.text = specChangeFrame:CreateFontString(nil, "OVERLAY")
    specChangeFrame.text:SetFontObject("GameFontHighlight")
    specChangeFrame.text:SetPoint("TOP", specChangeFrame, "TOP", 0, -40)
    specChangeFrame.text:SetText("Please reload your UI after changing specializations.\nSorry for the inconvenience!")
    specChangeFrame.text:SetWidth(specChangeFrame:GetWidth() - 20)

    local reloadButton = CreateFrame("Button", nil, specChangeFrame, "GameMenuButtonTemplate")
    reloadButton:SetPoint("BOTTOM", specChangeFrame, "BOTTOM", -80, 20)
    reloadButton:SetSize(140, 40)
    reloadButton:SetText("Reload UI")
    reloadButton:SetNormalFontObject("GameFontNormalLarge")
    reloadButton:SetHighlightFontObject("GameFontHighlightLarge")

    reloadButton:SetScript("OnClick", function()
        ReloadUI()
    end)

    local cancelButton = CreateFrame("Button", nil, specChangeFrame, "GameMenuButtonTemplate")
    cancelButton:SetPoint("BOTTOM", specChangeFrame, "BOTTOM", 80, 20)
    cancelButton:SetSize(140, 40)
    cancelButton:SetText("Cancel")
    cancelButton:SetNormalFontObject("GameFontNormalLarge")
    cancelButton:SetHighlightFontObject("GameFontHighlightLarge")

    cancelButton:SetScript("OnClick", function()
        specChangeFrame:Hide()
    end)

    local function GetCurrentSpecialization()
        -- MoP Classic compatibility
        if GetSpecialization and GetSpecializationInfo then
            return GetSpecializationInfo(GetSpecialization())
        else
            -- MoP Classic uses GetPrimaryTalentTree()
            return GetPrimaryTalentTree()
        end
    end

    local cachedSpecialization = GetCurrentSpecialization()

    local wowVersion, _, _, _ = GetBuildInfo()

    -- MoP Classic compatibility for spec change detection
    if GetSpecialization and GetSpecializationInfo then
        -- Retail/Modern WoW
        specChangeFrame:RegisterEvent("PLAYER_SPECIALIZATION_CHANGED")
        specChangeFrame:SetScript("OnEvent", function(self, event, unit)
            if unit ~= "player" then return end

            local currentSpecialization = GetCurrentSpecialization()

            if currentSpecialization ~= cachedSpecialization then
                cachedSpecialization = currentSpecialization
                self:Show()
            end
        end)
    else
        -- MoP Classic - use talent tree change events
        specChangeFrame:RegisterEvent("PLAYER_TALENT_UPDATE")
        specChangeFrame:SetScript("OnEvent", function(self, event)
            local currentSpecialization = GetCurrentSpecialization()

            if currentSpecialization ~= cachedSpecialization then
                cachedSpecialization = currentSpecialization
                self:Show()
            end
        end)
    end
end